<!-- Hero avec informations contextuelles -->
<div class="bg-body-light">
    <div class="content content-full">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <div>
                <h1 class="h3 fw-bold mb-1">
                    <i class="fa fa-address-book me-2 text-primary"></i>Contacts des Étudiants
                </h1>
                <div class="d-flex gap-3 text-muted fs-sm">
                    @if(isset($current_annee))
                        <span><i class="fa fa-calendar me-1"></i>{{ $current_annee->nom }}</span>
                    @endif
                    @if(isset($current_niveau))
                        <span><i class="fa fa-layer-group me-1"></i>{{ $current_niveau->nom }}</span>
                    @endif
                    @if(isset($current_parcours))
                        <span><i class="fa fa-graduation-cap me-1"></i>{{ $current_parcours->sigle }}</span>
                    @endif
                    <span><i class="fa fa-users me-1"></i>{{ count($etus) }} étudiants</span>
                </div>
            </div>
            <div class="d-flex gap-2">
                <button type="button" class="btn btn-sm btn-outline-secondary" wire:click="goToListResult">
                    <i class="fa fa-arrow-left me-1"></i> Retour aux filtres
                </button>
                <button type="button" class="btn btn-sm btn-primary" onclick="window.print()">
                    <i class="fa fa-print me-1"></i> Imprimer
                </button>
                <div class="dropdown">
                    <button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fa fa-download me-1"></i> Exporter
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="exportToCSV()">
                            <i class="fa fa-file-csv me-2"></i>CSV
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="exportToExcel()">
                            <i class="fa fa-file-excel me-2"></i>Excel
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="window.print()">
                            <i class="fa fa-file-pdf me-2"></i>PDF
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- END Hero -->

<!-- Page Content -->
<div class="content">
    <!-- Résumé des critères de sélection -->
    @if(isset($current_annee) || isset($current_niveau) || isset($current_parcours))
        <div class="alert alert-info d-print-none mb-3">
            <div class="d-flex align-items-center">
                <i class="fa fa-info-circle me-2"></i>
                <div>
                    <strong>Critères de sélection :</strong>
                    @if(isset($current_annee))
                        Année: <span class="badge bg-primary ms-1">{{ $current_annee->nom }}</span>
                    @endif
                    @if(isset($current_niveau))
                        Niveau: <span class="badge bg-success ms-1">{{ $current_niveau->nom }}</span>
                    @endif
                    @if(isset($current_parcours))
                        Parcours: <span class="badge bg-info ms-1">{{ $current_parcours->sigle }}</span>
                    @endif
                </div>
            </div>
        </div>
    @endif

    <!-- Table des contacts -->
    <div class="block block-rounded">
        <div class="block-header block-header-default d-print-none">
            <h3 class="block-title">
                <i class="fa fa-list me-2"></i>Liste des contacts étudiants
                <span class="badge bg-primary ms-2">{{ count($etus) }}</span>
            </h3>
            <div class="block-options">
                <div class="dropdown">
                    <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="fa fa-cog me-1"></i> Options d'affichage
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="toggleColumn('date-naissance')">
                            <i class="fa fa-birthday-cake me-2"></i>Date de naissance
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="toggleColumn('lieu-naissance')">
                            <i class="fa fa-map-marker-alt me-2"></i>Lieu de naissance
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="toggleColumn('adresse')">
                            <i class="fa fa-home me-2"></i>Adresse
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="toggleColumn('parents')">
                            <i class="fa fa-users me-2"></i>Noms des parents
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="block-content block-content-full">
            @if(count($etus) > 0)
                <div class="table-responsive">
                    <table class="table table-bordered table-striped table-vcenter" id="contactsTable">
                        <thead class="table-dark">
                            <tr>
                                <th class="text-center" style="width: 50px;">#</th>
                                <th class="text-center">
                                    <i class="fa fa-user me-1"></i>Nom et Prénom
                                </th>
                                <th class="text-center">
                                    <i class="fa fa-phone me-1"></i>Téléphone
                                </th>
                                <th class="text-center">
                                    <i class="fa fa-id-card me-1"></i>Matricule
                                </th>
                                <th class="text-center date-naissance">
                                    <i class="fa fa-birthday-cake me-1"></i>Date de naissance
                                </th>
                                <th class="text-center lieu-naissance">
                                    <i class="fa fa-map-marker-alt me-1"></i>Lieu de naissance
                                </th>
                                <th class="text-center adresse">
                                    <i class="fa fa-home me-1"></i>Adresse
                                </th>
                                <th class="text-center">
                                    <i class="fa fa-graduation-cap me-1"></i>Parcours
                                </th>
                                <th class="text-center">
                                    <i class="fa fa-layer-group me-1"></i>Niveau
                                </th>
                                <th class="text-center parents">
                                    <i class="fa fa-male me-1"></i>Nom du père
                                </th>
                                <th class="text-center parents">
                                    <i class="fa fa-female me-1"></i>Nom de la mère
                                </th>
                            </tr>
                        </thead>
                        <tbody>

                            @foreach ($etus as $etu)
                                @php
                                    // Récupérer les informations d'inscription les plus récentes
                                    $inscription = $etu->info->first();
                                @endphp
                                <tr class="student-row">
                                    <td class="text-center fw-bold text-primary">{{ $loop->iteration }}</td>
                                    <td class="fw-semibold">
                                        <div class="d-flex align-items-center">
                                            @if($etu->photo)
                                                <img src="{{ asset('storage/' . $etu->photo) }}"
                                                     alt="Photo de {{ $etu->nom }}"
                                                     class="rounded-circle me-2"
                                                     style="width: 40px; height: 40px; object-fit: cover;">
                                            @else
                                                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2"
                                                     style="width: 40px; height: 40px; font-size: 16px; font-weight: bold;">
                                                    {{ substr($etu->nom, 0, 1) }}{{ substr($etu->prenom, 0, 1) }}
                                                </div>
                                            @endif
                                            <div>
                                                <div class="fw-bold text-dark">{{ $etu->nom }} {{ $etu->prenom }}</div>
                                                @if($etu->sexe)
                                                    <small class="text-muted">
                                                        <i class="fa fa-{{ $etu->sexe === 'M' ? 'male text-primary' : 'female text-danger' }} me-1"></i>
                                                        {{ $etu->sexe === 'M' ? 'Masculin' : 'Féminin' }}
                                                    </small>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        <div class="fw-semibold text-primary">
                                            <i class="fa fa-phone me-1"></i>{{ $etu->telephone1 ?: 'N/A' }}
                                        </div>
                                        @if($etu->telephone2)
                                            <small class="text-muted">
                                                <i class="fa fa-mobile-alt me-1"></i>{{ $etu->telephone2 }}
                                            </small>
                                        @endif
                                    </td>
                                    <td class="text-center">
                                        @if($etu->matricule)
                                            <span class="badge bg-secondary fs-6">{{ $etu->matricule }}</span>
                                        @else
                                            <span class="text-muted">Non attribué</span>
                                        @endif
                                    </td>
                                    <td class="text-center date-naissance">
                                        @if($etu->date_naissance)
                                            <div class="fw-semibold">{{ \Carbon\Carbon::parse($etu->date_naissance)->format('d/m/Y') }}</div>
                                            <small class="text-muted">{{ \Carbon\Carbon::parse($etu->date_naissance)->age }} ans</small>
                                        @else
                                            <span class="text-muted">Non renseignée</span>
                                        @endif
                                    </td>
                                    <td class="text-center lieu-naissance">
                                        <span class="text-muted">{{ $etu->lieu_naissance ?: 'Non renseigné' }}</span>
                                    </td>
                                    <td class="text-center adresse">
                                        <span class="text-muted">{{ $etu->adresse ?: 'Non renseignée' }}</span>
                                    </td>
                                    <td class="text-center">
                                        @if($inscription && $inscription->parcours)
                                            <div>
                                                <span class="badge bg-info fs-6">{{ $inscription->parcours->sigle }}</span>
                                                <div class="small text-muted mt-1">{{ $inscription->parcours->nom }}</div>
                                            </div>
                                        @else
                                            <span class="badge bg-warning">Pas de parcours</span>
                                        @endif
                                    </td>
                                    <td class="text-center">
                                        @if($inscription && $inscription->niveau)
                                            <span class="badge bg-success fs-6">{{ $inscription->niveau->nom }}</span>
                                        @else
                                            <span class="badge bg-warning">N/A</span>
                                        @endif
                                    </td>
                                    <td class="text-center parents">
                                        <div class="fw-semibold text-dark">
                                            <i class="fa fa-male me-1 text-primary"></i>
                                            {{ $etu->nom_pere ?: 'Non renseigné' }}
                                        </div>
                                    </td>
                                    <td class="text-center parents">
                                        <div class="fw-semibold text-dark">
                                            <i class="fa fa-female me-1 text-danger"></i>
                                            {{ $etu->nom_mere ?: 'Non renseigné' }}
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Statistiques et résumé -->
                <div class="row mt-4 d-print-none">
                    <div class="col-md-6">
                        <div class="card border-primary">
                            <div class="card-header bg-primary text-white">
                                <h6 class="card-title mb-0">
                                    <i class="fa fa-chart-pie me-2"></i>Statistiques
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-4">
                                        <div class="fw-bold text-primary fs-4">{{ count($etus) }}</div>
                                        <small class="text-muted">Total étudiants</small>
                                    </div>
                                    <div class="col-4">
                                        <div class="fw-bold text-success fs-4">
                                            {{ collect($etus)->where('sexe', 'M')->count() }}
                                        </div>
                                        <small class="text-muted">Hommes</small>
                                    </div>
                                    <div class="col-4">
                                        <div class="fw-bold text-danger fs-4">
                                            {{ collect($etus)->where('sexe', 'F')->count() }}
                                        </div>
                                        <small class="text-muted">Femmes</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card border-info">
                            <div class="card-header bg-info text-white">
                                <h6 class="card-title mb-0">
                                    <i class="fa fa-info-circle me-2"></i>Informations
                                </h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fa fa-calendar text-primary me-2"></i>
                                        Généré le {{ \Carbon\Carbon::now()->format('d/m/Y à H:i') }}
                                    </li>
                                    <li><i class="fa fa-user text-success me-2"></i>
                                        Par {{ auth()->user()->nom }} {{ auth()->user()->prenom }}
                                    </li>
                                    @if(isset($current_annee))
                                        <li><i class="fa fa-graduation-cap text-info me-2"></i>
                                            Année universitaire {{ $current_annee->nom }}
                                        </li>
                                    @endif
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            @else
                <!-- État vide -->
                <div class="text-center py-5">
                    <div class="mb-3">
                        <i class="fa fa-users fa-4x text-muted"></i>
                    </div>
                    <h4 class="text-muted">Aucun étudiant trouvé</h4>
                    <p class="text-muted">
                        Aucun étudiant ne correspond aux critères sélectionnés.
                    </p>
                    <button type="button" class="btn btn-primary" wire:click="goToListResult">
                        <i class="fa fa-arrow-left me-1"></i> Modifier les critères
                    </button>
                </div>
            @endif
        </div>
    </div>
    <!-- END Dynamic Table Full -->
</div>
<!-- END Page Content -->

<!-- Scripts pour les fonctionnalités d'export et d'affichage -->
<script>
function toggleColumn(className) {
    const elements = document.querySelectorAll('.' + className);
    elements.forEach(element => {
        element.style.display = element.style.display === 'none' ? '' : 'none';
    });
}

function exportToCSV() {
    const table = document.getElementById('contactsTable');
    const rows = Array.from(table.querySelectorAll('tr'));

    const csvContent = rows.map(row => {
        const cells = Array.from(row.querySelectorAll('th, td'));
        return cells.map(cell => {
            const text = cell.textContent.trim().replace(/\s+/g, ' ');
            return '"' + text.replace(/"/g, '""') + '"';
        }).join(',');
    }).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = 'contacts_etudiants_' + new Date().toISOString().split('T')[0] + '.csv';
    link.click();
}

function exportToExcel() {
    // Implémentation simplifiée - nécessiterait une bibliothèque comme SheetJS pour une vraie export Excel
    exportToCSV();
}

// Styles d'impression
const printStyles = `
<style>
@media print {
    .d-print-none { display: none !important; }
    .table { font-size: 12px; }
    .badge {
        background-color: #6c757d !important;
        color: white !important;
        -webkit-print-color-adjust: exact;
    }
    .bg-primary {
        background-color: #0d6efd !important;
        -webkit-print-color-adjust: exact;
    }
    .text-primary {
        color: #0d6efd !important;
        -webkit-print-color-adjust: exact;
    }
    .student-row:nth-child(even) {
        background-color: #f8f9fa !important;
        -webkit-print-color-adjust: exact;
    }
}
</style>
`;

document.head.insertAdjacentHTML('beforeend', printStyles);
</script>
