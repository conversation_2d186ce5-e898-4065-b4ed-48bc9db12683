{"__meta": {"id": "Xa155079f6306ff6d4add38af64299e89", "datetime": "2025-07-24 11:15:14", "utime": 1753344914.00784, "method": "GET", "uri": "/login", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753344912.676577, "end": 1753344914.007869, "duration": 1.331291913986206, "duration_str": "1.33s", "measures": [{"label": "Booting", "start": 1753344912.676577, "relative_start": 0, "end": 1753344913.694068, "relative_end": 1753344913.694068, "duration": 1.0174908638000488, "duration_str": "1.02s", "params": [], "collector": null}, {"label": "Application", "start": 1753344913.695015, "relative_start": 1.0184378623962402, "end": 1753344914.007871, "relative_end": 1.9073486328125e-06, "duration": 0.31285595893859863, "duration_str": "313ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 23529128, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "auth.login (\\resources\\views\\auth\\login.blade.php)", "param_count": 0, "params": [], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/auth/login.blade.php&line=0"}, {"name": "layouts.simple (\\resources\\views\\layouts\\simple.blade.php)", "param_count": 3, "params": ["__env", "app", "errors"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/layouts/simple.blade.php&line=0"}]}, "route": {"uri": "GET login", "middleware": "web", "controller": "App\\Http\\Controllers\\Auth\\LoginController@showLoginForm", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php&line=19\">\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php:19-22</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "statements": []}, "models": {"data": [], "count": 0}, "livewire": {"data": [], "count": 0}, "gate": {"count": 0, "messages": []}, "session": {"_token": "3GwvPj4Apcc950mCLXAMLIm89gj9WEN9AL6bHnr3", "url": "array:1 [\n  \"intended\" => \"http://127.0.0.1:8000/caisse/infoetus\"\n]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-949960624 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-949960624\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1666222777 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1666222777\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1562915231 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1562915231\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1329088382 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"721 characters\">XSRF-TOKEN=eyJpdiI6InRjelcrcm1BQk92ejdFTDJXQ2Uvdmc9PSIsInZhbHVlIjoidnR1TU9QZEdkckhNNXppWThMMjdIMkZtc3ljMEh2TS9DZkZMMmpPbG1ScTNjTXcvalhRSVh6OFJrTjhnRjJjbDRwaS9RZHBYakNoZ084dEVvNThlOXloeWpaK1FEYU95bEFLVUs4YndBV1k2UHZFdjF6STh1Q3Z3UE9Ba1B6eWciLCJtYWMiOiIwYjgyMTMzNDg1NzM5MmZiZjFiYTIyMTE5NDZlNzc1ZjM1MmJjNmRkY2E2ZjYzZmUwNDU1ZTU4N2MyOTlmNjE1IiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6Ii94YjN5NFFPY3ZLeDZucHVwNXE5SFE9PSIsInZhbHVlIjoiWEk0aWE4dUtsSTVKRGNXV2FUYStiOVNCcXBhV3h1L3owLzF4bkp4WWx4UTlnVS84V1RyM2NFQ0ZGNUlleUNkbHYzKzZydFowVUFvU1U2SEs5VlJiekRkZjBUNFlnN09tQ1E5Snk0aTZENnVyeWxzTU5YeXBXTFBYakdVWDdJNnkiLCJtYWMiOiJhMzk2OWE0MDY0MjRlMjk3MDYxYjQ3ZThkOGQ5OTJkNmQ2OGU0ZjRiZjU1MmQ3MTg1ZjRmMWIxZmY5NmY0ZDA5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1329088382\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1107895789 data-indent-pad=\"  \"><span class=sf-dump-note>array:30</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"35 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">52975</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"6 characters\">/login</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"45 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"6 characters\">/login</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"16 characters\">/index.php/login</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"721 characters\">XSRF-TOKEN=eyJpdiI6InRjelcrcm1BQk92ejdFTDJXQ2Uvdmc9PSIsInZhbHVlIjoidnR1TU9QZEdkckhNNXppWThMMjdIMkZtc3ljMEh2TS9DZkZMMmpPbG1ScTNjTXcvalhRSVh6OFJrTjhnRjJjbDRwaS9RZHBYakNoZ084dEVvNThlOXloeWpaK1FEYU95bEFLVUs4YndBV1k2UHZFdjF6STh1Q3Z3UE9Ba1B6eWciLCJtYWMiOiIwYjgyMTMzNDg1NzM5MmZiZjFiYTIyMTE5NDZlNzc1ZjM1MmJjNmRkY2E2ZjYzZmUwNDU1ZTU4N2MyOTlmNjE1IiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6Ii94YjN5NFFPY3ZLeDZucHVwNXE5SFE9PSIsInZhbHVlIjoiWEk0aWE4dUtsSTVKRGNXV2FUYStiOVNCcXBhV3h1L3owLzF4bkp4WWx4UTlnVS84V1RyM2NFQ0ZGNUlleUNkbHYzKzZydFowVUFvU1U2SEs5VlJiekRkZjBUNFlnN09tQ1E5Snk0aTZENnVyeWxzTU5YeXBXTFBYakdVWDdJNnkiLCJtYWMiOiJhMzk2OWE0MDY0MjRlMjk3MDYxYjQ3ZThkOGQ5OTJkNmQ2OGU0ZjRiZjU1MmQ3MTg1ZjRmMWIxZmY5NmY0ZDA5IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753344912.6766</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753344912</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1107895789\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1400315871 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3GwvPj4Apcc950mCLXAMLIm89gj9WEN9AL6bHnr3</span>\"\n  \"<span class=sf-dump-key>scolarite_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Ifvbropdy9bkgErK24v5HqcPwgxmKEdRv2bljKL2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1400315871\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-817086666 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 24 Jul 2025 08:15:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImxJaDdqeDhIOGh6RnpYcHEvaFBpeGc9PSIsInZhbHVlIjoiVGsrYTZJN3l4cW9VVEtaVGhrbGY2TDRSRnhTUTVJdlVrQ2orZGQ1czE3bFJPbWt3VnZ0QVJUczh2NXFWZTVGUjR3Z3VMZ2Fyb0dEb2VPS1pnZDlTV1A1aTh5bkVndTBSdjdNN0RsNVBPclNjdkVVQU1DMitVMjRURXJMRTNmK3giLCJtYWMiOiJiZWRjM2MxNzEyOGQwZTFmOGI4NTdkZTkxMWVlMWExZjI0NmZlNTU4OGU5Y2EyNDVkYzk4ZDdlNWVjYzc1YTBmIiwidGFnIjoiIn0%3D; expires=Thu, 24 Jul 2025 10:15:13 GMT; Max-Age=7199; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"451 characters\">scolarite_imsaa_session=eyJpdiI6ImFieVRZdmprem5kemVOYm51RzVjb1E9PSIsInZhbHVlIjoiUGl2UjZLK3o2VDNlL1Nuc29kU0RZdU1hSDllemgrdXN0NjkrVVErZ0pLOFlMSEQ4dklQMmhJMlR6ZlFSY202d29PSytxc0NySVFpNXRndzhsZmpsNk9KdU5PNE9lKzJ0NVFTejlhSWlNcnE5end3aXRVR3N2MTRaMUlmSWMzVWIiLCJtYWMiOiIwODM2YjU3M2FkNjgzOTQxNDgyYTcxNjlkYjY3NDNlMDVmZGZiOGIxOTMyMjBhNTk3MDhhMTliNWY0OGIxNjI1IiwidGFnIjoiIn0%3D; expires=Thu, 24 Jul 2025 10:15:13 GMT; Max-Age=7199; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImxJaDdqeDhIOGh6RnpYcHEvaFBpeGc9PSIsInZhbHVlIjoiVGsrYTZJN3l4cW9VVEtaVGhrbGY2TDRSRnhTUTVJdlVrQ2orZGQ1czE3bFJPbWt3VnZ0QVJUczh2NXFWZTVGUjR3Z3VMZ2Fyb0dEb2VPS1pnZDlTV1A1aTh5bkVndTBSdjdNN0RsNVBPclNjdkVVQU1DMitVMjRURXJMRTNmK3giLCJtYWMiOiJiZWRjM2MxNzEyOGQwZTFmOGI4NTdkZTkxMWVlMWExZjI0NmZlNTU4OGU5Y2EyNDVkYzk4ZDdlNWVjYzc1YTBmIiwidGFnIjoiIn0%3D; expires=Thu, 24-Jul-2025 10:15:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"423 characters\">scolarite_imsaa_session=eyJpdiI6ImFieVRZdmprem5kemVOYm51RzVjb1E9PSIsInZhbHVlIjoiUGl2UjZLK3o2VDNlL1Nuc29kU0RZdU1hSDllemgrdXN0NjkrVVErZ0pLOFlMSEQ4dklQMmhJMlR6ZlFSY202d29PSytxc0NySVFpNXRndzhsZmpsNk9KdU5PNE9lKzJ0NVFTejlhSWlNcnE5end3aXRVR3N2MTRaMUlmSWMzVWIiLCJtYWMiOiIwODM2YjU3M2FkNjgzOTQxNDgyYTcxNjlkYjY3NDNlMDVmZGZiOGIxOTMyMjBhNTk3MDhhMTliNWY0OGIxNjI1IiwidGFnIjoiIn0%3D; expires=Thu, 24-Jul-2025 10:15:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-817086666\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1289866200 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3GwvPj4Apcc950mCLXAMLIm89gj9WEN9AL6bHnr3</span>\"\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/caisse/infoetus</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1289866200\", {\"maxDepth\":0})</script>\n"}}