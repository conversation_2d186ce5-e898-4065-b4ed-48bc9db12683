{"__meta": {"id": "X3a925fb3e113fa719d330ddfca8b040b", "datetime": "2025-07-24 11:31:16", "utime": 1753345876.903923, "method": "POST", "uri": "/livewire/message/liste-new-etu", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753345875.349503, "end": 1753345876.903962, "duration": 1.5544588565826416, "duration_str": "1.55s", "measures": [{"label": "Booting", "start": 1753345875.349503, "relative_start": 0, "end": 1753345876.300736, "relative_end": 1753345876.300736, "duration": 0.95123291015625, "duration_str": "951ms", "params": [], "collector": null}, {"label": "Application", "start": 1753345876.301502, "relative_start": 0.9519989490509033, "end": 1753345876.903966, "relative_end": 4.0531158447265625e-06, "duration": 0.602463960647583, "duration_str": "602ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 26814184, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 3, "templates": [{"name": "livewire.administration.contactEtu.index (\\resources\\views\\livewire\\administration\\contactEtu\\index.blade.php)", "param_count": 27, "params": ["etudiants", "parcourse", "niveaux", "annees", "livewireLayout", "errors", "_instance", "currentPage", "filtreParcours", "filtre<PERSON><PERSON>au", "filtreAnnee", "query", "perPage", "isLoading", "sortField", "sortDirection", "showFilters", "compactView", "totalEtudiants", "totalParParcours", "newEtus", "etus", "current_parcours", "current_niveau", "current_annee", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/administration/contactEtu/index.blade.php&line=0"}, {"name": "livewire.administration.contactEtu.liste (\\resources\\views\\livewire\\administration\\contactEtu\\liste.blade.php)", "param_count": 29, "params": ["__env", "app", "errors", "_instance", "etudiants", "parcourse", "niveaux", "annees", "livewireLayout", "currentPage", "filtreParcours", "filtre<PERSON><PERSON>au", "filtreAnnee", "query", "perPage", "isLoading", "sortField", "sortDirection", "showFilters", "compactView", "totalEtudiants", "totalParParcours", "newEtus", "etus", "current_parcours", "current_niveau", "current_annee", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/administration/contactEtu/liste.blade.php&line=0"}, {"name": "livewire::bootstrap (\\vendor\\livewire\\livewire\\src\\views\\pagination\\bootstrap.blade.php)", "param_count": 2, "params": ["paginator", "elements"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\vendor\\livewire\\livewire\\src\\views\\pagination/bootstrap.blade.php&line=0"}]}, "route": {"uri": "POST livewire/message/{name}", "uses": "Livewire\\Controllers\\HttpConnectionHandler@__invoke", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "as": "livewire.message", "middleware": "web"}, "queries": {"nb_statements": 10, "nb_failed_statements": 0, "accumulated_duration": 0.22616999999999998, "accumulated_duration_str": "226ms", "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00674, "duration_str": "6.74ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:59", "connection": "imsaaapp", "start_percent": 0, "width_percent": 2.98}, {"sql": "select count(*) as aggregate from `users` where exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `role_id` = 5) and exists (select * from `inscription_students` where `users`.`id` = `inscription_students`.`user_id` and `annee_universitaire_id` = '4' and `parcour_id` = '1' and `inscription_students`.`deleted_at` is null) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "4", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 174}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.03555, "duration_str": "35.55ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:174", "connection": "imsaaapp", "start_percent": 2.98, "width_percent": 15.718}, {"sql": "select * from `users` where exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `role_id` = 5) and exists (select * from `inscription_students` where `users`.`id` = `inscription_students`.`user_id` and `annee_universitaire_id` = '4' and `parcour_id` = '1' and `inscription_students`.`deleted_at` is null) and `users`.`deleted_at` is null order by `nom` asc limit 10 offset 0", "type": "query", "params": [], "bindings": ["5", "4", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 174}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00926, "duration_str": "9.26ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:174", "connection": "imsaaapp", "start_percent": 18.698, "width_percent": 4.094}, {"sql": "select * from `inscription_students` where `inscription_students`.`user_id` in (7, 8, 12, 16, 22, 25, 31, 34, 36, 190) and `annee_universitaire_id` = '4' and `parcour_id` = '1' and `inscription_students`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["4", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 174}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00136, "duration_str": "1.36ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:174", "connection": "imsaaapp", "start_percent": 22.793, "width_percent": 0.601}, {"sql": "select `id`, `sigle`, `nom` from `parcours` where `parcours`.`id` in (1) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 174}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0012900000000000001, "duration_str": "1.29ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:174", "connection": "imsaaapp", "start_percent": 23.394, "width_percent": 0.57}, {"sql": "select `id`, `nom` from `niveaux` where `niveaux`.`id` in (1, 2) and `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 174}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0008100000000000001, "duration_str": "810μs", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:174", "connection": "imsaaapp", "start_percent": 23.964, "width_percent": 0.358}, {"sql": "select `id`, `nom` from `annee_universitaires` where `annee_universitaires`.`id` in (4) and `annee_universitaires`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 174}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00073, "duration_str": "730μs", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:174", "connection": "imsaaapp", "start_percent": 24.322, "width_percent": 0.323}, {"sql": "select * from `parcours` where `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 178}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.04875, "duration_str": "48.75ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:178", "connection": "imsaaapp", "start_percent": 24.645, "width_percent": 21.555}, {"sql": "select * from `niveaux` where `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 179}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0578, "duration_str": "57.8ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:179", "connection": "imsaaapp", "start_percent": 46.2, "width_percent": 25.556}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`deleted_at` is null order by `nom` desc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 180}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.06388, "duration_str": "63.88ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:180", "connection": "imsaaapp", "start_percent": 71.756, "width_percent": 28.244}]}, "models": {"data": {"App\\Models\\AnneeUniversitaire": 7, "App\\Models\\Niveau": 7, "App\\Models\\Parcour": 25, "App\\Models\\InscriptionStudent": 10, "App\\Models\\User": 11}, "count": 60}, "livewire": {"data": {"liste-new-etu #bSj1DTIMuQEsLxmYlGr9": "array:5 [\n  \"data\" => array:20 [\n    \"currentPage\" => \"liste\"\n    \"filtreParcours\" => \"1\"\n    \"filtreNiveau\" => \"\"\n    \"filtreAnnee\" => \"4\"\n    \"query\" => \"\"\n    \"perPage\" => \"10\"\n    \"isLoading\" => false\n    \"sortField\" => \"nom\"\n    \"sortDirection\" => \"asc\"\n    \"showFilters\" => true\n    \"compactView\" => true\n    \"totalEtudiants\" => 238\n    \"totalParParcours\" => array:11 [\n      \"MAT\" => 1\n      \"THR\" => 46\n      \"CM\" => 24\n      \"GFC\" => 30\n      \"TBA\" => 17\n      \"TD\" => 72\n      \"GBC\" => 12\n      \"GPM\" => 6\n      \"GEE\" => 3\n      \"LCI\" => 22\n      \"ISE\" => 5\n    ]\n    \"newEtus\" => []\n    \"etus\" => []\n    \"current_parcours\" => null\n    \"current_niveau\" => null\n    \"current_annee\" => null\n    \"page\" => 1\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"liste-new-etu\"\n  \"view\" => \"livewire.administration.contactEtu.index\"\n  \"component\" => \"App\\Http\\Livewire\\ListeNewEtu\"\n  \"id\" => \"bSj1DTIMuQEsLxmYlGr9\"\n]"}, "count": 1}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VtnJlwxOpd0hRXXY8oKOSLlbl0TFeAO2LM004ptd", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/caisse/infoetus\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1753344491\n]"}, "request": {"path_info": "/livewire/message/liste-new-etu", "status_code": "<pre class=sf-dump id=sf-dump-1506897532 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1506897532\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-684120071 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-684120071\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-5377144 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">bSj1DTIMuQEsLxmYlGr9</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">liste-new-etu</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"15 characters\">caisse/infoetus</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => []\n    \"<span class=sf-dump-key>errors</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>newEtus.niveau_id</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">Le champ new etus.niveau id est obligatoire.</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>newEtus.parcour_id</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"45 characters\">Le champ new etus.parcour id est obligatoire.</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>newEtus.annee_universitaire_id</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"57 characters\">Le champ new etus.annee universitaire id est obligatoire.</span>\"\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">0593e676</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:20</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>currentPage</span>\" => \"<span class=sf-dump-str title=\"5 characters\">liste</span>\"\n      \"<span class=sf-dump-key>filtreParcours</span>\" => \"<span class=sf-dump-str>1</span>\"\n      \"<span class=sf-dump-key>filtreNiveau</span>\" => \"\"\n      \"<span class=sf-dump-key>filtreAnnee</span>\" => \"<span class=sf-dump-str>4</span>\"\n      \"<span class=sf-dump-key>query</span>\" => \"\"\n      \"<span class=sf-dump-key>perPage</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n      \"<span class=sf-dump-key>isLoading</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>sortField</span>\" => \"<span class=sf-dump-str title=\"3 characters\">nom</span>\"\n      \"<span class=sf-dump-key>sortDirection</span>\" => \"<span class=sf-dump-str title=\"3 characters\">asc</span>\"\n      \"<span class=sf-dump-key>showFilters</span>\" => <span class=sf-dump-const>true</span>\n      \"<span class=sf-dump-key>compactView</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>totalEtudiants</span>\" => <span class=sf-dump-num>238</span>\n      \"<span class=sf-dump-key>totalParParcours</span>\" => <span class=sf-dump-note>array:11</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>MAT</span>\" => <span class=sf-dump-num>1</span>\n        \"<span class=sf-dump-key>THR</span>\" => <span class=sf-dump-num>46</span>\n        \"<span class=sf-dump-key>CM</span>\" => <span class=sf-dump-num>24</span>\n        \"<span class=sf-dump-key>GFC</span>\" => <span class=sf-dump-num>30</span>\n        \"<span class=sf-dump-key>TBA</span>\" => <span class=sf-dump-num>17</span>\n        \"<span class=sf-dump-key>TD</span>\" => <span class=sf-dump-num>72</span>\n        \"<span class=sf-dump-key>GBC</span>\" => <span class=sf-dump-num>12</span>\n        \"<span class=sf-dump-key>GPM</span>\" => <span class=sf-dump-num>6</span>\n        \"<span class=sf-dump-key>GEE</span>\" => <span class=sf-dump-num>3</span>\n        \"<span class=sf-dump-key>LCI</span>\" => <span class=sf-dump-num>22</span>\n        \"<span class=sf-dump-key>ISE</span>\" => <span class=sf-dump-num>5</span>\n      </samp>]\n      \"<span class=sf-dump-key>newEtus</span>\" => []\n      \"<span class=sf-dump-key>etus</span>\" => []\n      \"<span class=sf-dump-key>current_parcours</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>current_niveau</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>current_annee</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>paginators</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => []\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">088ee9e074ab53b1ef8c8a0cff558e3e03f5bdd9092f9d21ecdebf0d8486fd24</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">w579</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"17 characters\">toggleCompactView</span>\"\n        \"<span class=sf-dump-key>params</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-5377144\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1707378636 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1089</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">VtnJlwxOpd0hRXXY8oKOSLlbl0TFeAO2LM004ptd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/caisse/infoetus</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImQvWWlGM3AwakdWeVR1dWpqaDVma2c9PSIsInZhbHVlIjoiSm5yTjA2RzZGUGJLaXJwRkx2cWUyREpQeGhyS1RreEVKWmdoalVhTm5lQWpieEk1eHVPTE1yaXlLOHRzcytFOWlRY2g3K09SbnpRRE1sWWtKWk05bkZFeGhmekdsS21VY0tPRGpSU21WOHlmUTgwbUwzRWJvWUw0NUtheDNPZ2EiLCJtYWMiOiJlZGRmNmE2NGFiZmZlYmMxNWVjZjY2OTU4YzI2OWQyMmZkMTcwMmUzZGMzMjllMWNmZGE4NTZmMDVjMWNmNTUyIiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6ImwzRmFXdkxSejA5dXBQVVYvRFJqK2c9PSIsInZhbHVlIjoiQmMxblFEcFV5MTh2ZS9SREpjYWs4a0dVdDlXYXVMTHdlZXp5RkpnNXg0Z1JxVnB1QUV1TnhiRjk4OS81bVRPVjNKVVVoNDRvNE9KandpSE1oMG95WS8rSjBPWnBCNEhDcng1S0luZ2JReVlpdlFIcU5qNldvc2lSaTMyWTJQREYiLCJtYWMiOiIwYzRjOTAzMTU4MzNlN2IxMjhjNzgyNjkzZjE1MzRhM2Y5ZDNmOWRkMzJlMWQ4NjYxMWJkYTNlMDcyYWY5ZGIzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1707378636\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-838240698 data-indent-pad=\"  \"><span class=sf-dump-note>array:36</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"35 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">53801</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"31 characters\">/livewire/message/liste-new-etu</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"45 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"31 characters\">/livewire/message/liste-new-etu</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"41 characters\">/index.php/livewire/message/liste-new-etu</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1089</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1089</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_X_LIVEWIRE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/caisse/infoetus</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImQvWWlGM3AwakdWeVR1dWpqaDVma2c9PSIsInZhbHVlIjoiSm5yTjA2RzZGUGJLaXJwRkx2cWUyREpQeGhyS1RreEVKWmdoalVhTm5lQWpieEk1eHVPTE1yaXlLOHRzcytFOWlRY2g3K09SbnpRRE1sWWtKWk05bkZFeGhmekdsS21VY0tPRGpSU21WOHlmUTgwbUwzRWJvWUw0NUtheDNPZ2EiLCJtYWMiOiJlZGRmNmE2NGFiZmZlYmMxNWVjZjY2OTU4YzI2OWQyMmZkMTcwMmUzZGMzMjllMWNmZGE4NTZmMDVjMWNmNTUyIiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6ImwzRmFXdkxSejA5dXBQVVYvRFJqK2c9PSIsInZhbHVlIjoiQmMxblFEcFV5MTh2ZS9SREpjYWs4a0dVdDlXYXVMTHdlZXp5RkpnNXg0Z1JxVnB1QUV1TnhiRjk4OS81bVRPVjNKVVVoNDRvNE9KandpSE1oMG95WS8rSjBPWnBCNEhDcng1S0luZ2JReVlpdlFIcU5qNldvc2lSaTMyWTJQREYiLCJtYWMiOiIwYzRjOTAzMTU4MzNlN2IxMjhjNzgyNjkzZjE1MzRhM2Y5ZDNmOWRkMzJlMWQ4NjYxMWJkYTNlMDcyYWY5ZGIzIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753345875.3495</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753345875</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-838240698\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-40259516 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VtnJlwxOpd0hRXXY8oKOSLlbl0TFeAO2LM004ptd</span>\"\n  \"<span class=sf-dump-key>scolarite_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RKkczZOcD1TSWkk2eKq2gL5rBSt8JPLoI0Me9VkZ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-40259516\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-5226550 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 24 Jul 2025 08:31:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InFrdnprMjFST0MycFQwd2QvRFo1T3c9PSIsInZhbHVlIjoiY0FMR0x5NlhHcjdoL2pUekRud3QvYjFjSEZWSmxYdGdpZGw3QWlMVkcrTUdOTi9xMm1IQy9XMEE3b1NXWEVpSHRHMnhBakdrQVNhY2wxTElDRTRqOFl0b0NZR0RwQXNiV0RIY3kyZkV5ODVCSGwwbGJpRkxTVlBmTmtnTStkNU4iLCJtYWMiOiJmNmMzYTIzNTU5ZGQwMTg2YjYyM2RiMjQ2NGY5Y2JiMzJmNGNjZDE4NGEzNjE1NGYwN2YwN2UzNDlhODkxZjI1IiwidGFnIjoiIn0%3D; expires=Thu, 24 Jul 2025 10:31:16 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"451 characters\">scolarite_imsaa_session=eyJpdiI6InVNb3prYk4vRTk0eVY3VmR1UlNsTkE9PSIsInZhbHVlIjoiWERyYVhhMCtJY0M0L2w5SDN6K2t3R2RwMWJuVWZqNkt1cDVnZ3l2UmhLalNIV2YwdWc1WThaa3FOcWxVWjk1bytYTEtabGZwVjJQMExNNGFlNTZhSjJLaWU1VjZlZXNGMXdXYVBxbVRRVGtubitsUDY4dTJxdTR2dGV2S3RsS1EiLCJtYWMiOiI3ZjI0NzY0MzY3Y2NlMTVjNjJjNzc4YzliMGY4ODgwZTQyMjdkYTMxYzQzMTZiYjA2NWYyNTExZGU5OWRiZjA4IiwidGFnIjoiIn0%3D; expires=Thu, 24 Jul 2025 10:31:16 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InFrdnprMjFST0MycFQwd2QvRFo1T3c9PSIsInZhbHVlIjoiY0FMR0x5NlhHcjdoL2pUekRud3QvYjFjSEZWSmxYdGdpZGw3QWlMVkcrTUdOTi9xMm1IQy9XMEE3b1NXWEVpSHRHMnhBakdrQVNhY2wxTElDRTRqOFl0b0NZR0RwQXNiV0RIY3kyZkV5ODVCSGwwbGJpRkxTVlBmTmtnTStkNU4iLCJtYWMiOiJmNmMzYTIzNTU5ZGQwMTg2YjYyM2RiMjQ2NGY5Y2JiMzJmNGNjZDE4NGEzNjE1NGYwN2YwN2UzNDlhODkxZjI1IiwidGFnIjoiIn0%3D; expires=Thu, 24-Jul-2025 10:31:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"423 characters\">scolarite_imsaa_session=eyJpdiI6InVNb3prYk4vRTk0eVY3VmR1UlNsTkE9PSIsInZhbHVlIjoiWERyYVhhMCtJY0M0L2w5SDN6K2t3R2RwMWJuVWZqNkt1cDVnZ3l2UmhLalNIV2YwdWc1WThaa3FOcWxVWjk1bytYTEtabGZwVjJQMExNNGFlNTZhSjJLaWU1VjZlZXNGMXdXYVBxbVRRVGtubitsUDY4dTJxdTR2dGV2S3RsS1EiLCJtYWMiOiI3ZjI0NzY0MzY3Y2NlMTVjNjJjNzc4YzliMGY4ODgwZTQyMjdkYTMxYzQzMTZiYjA2NWYyNTExZGU5OWRiZjA4IiwidGFnIjoiIn0%3D; expires=Thu, 24-Jul-2025 10:31:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-5226550\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2033921352 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VtnJlwxOpd0hRXXY8oKOSLlbl0TFeAO2LM004ptd</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/caisse/infoetus</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1753344491</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2033921352\", {\"maxDepth\":0})</script>\n"}}