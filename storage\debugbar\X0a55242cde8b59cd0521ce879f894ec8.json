{"__meta": {"id": "X0a55242cde8b59cd0521ce879f894ec8", "datetime": "2025-07-24 11:08:11", "utime": **********.629549, "method": "POST", "uri": "/login", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753344478.343528, "end": **********.629576, "duration": 13.28604793548584, "duration_str": "13.29s", "measures": [{"label": "Booting", "start": 1753344478.343528, "relative_start": 0, "end": 1753344479.832379, "relative_end": 1753344479.832379, "duration": 1.4888510704040527, "duration_str": "1.49s", "params": [], "collector": null}, {"label": "Application", "start": 1753344479.833614, "relative_start": 1.4900860786437988, "end": **********.629579, "relative_end": 3.0994415283203125e-06, "duration": 11.79596495628357, "duration_str": "11.8s", "params": [], "collector": null}]}, "memory": {"peak_usage": 25875632, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST login", "middleware": "web", "controller": "App\\Http\\Controllers\\Auth\\LoginController@login", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php&line=32\">\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php:32-60</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.56689, "accumulated_duration_str": "567ms", "statements": [{"sql": "select * from `users` where `email` = 'tcadmin' and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["tcadmin"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 139}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 381}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php", "line": 86}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\ui\\auth-backend\\AuthenticatesUsers.php", "line": 46}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.56689, "duration_str": "567ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:139", "connection": "imsaaapp", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\User": 1}, "count": 1}, "livewire": {"data": [], "count": 0}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VtnJlwxOpd0hRXXY8oKOSLlbl0TFeAO2LM004ptd", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/login\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => **********\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-752806159 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-752806159\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2083270753 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NYEHn10iGV9qMVXxamnzO9bZQWyfAnf8NbmLax09</span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"7 characters\">tcadmin</span>\"\n  \"<span class=sf-dump-key>password</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Imsaa21?!</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2083270753\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1985368072 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">84</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlRxZGU1YjJ5eitRclllSlAzVVJXZlE9PSIsInZhbHVlIjoiWEhGczZHQk52UXhJTnNTSzZmc2psMVFJcjBuTG0ycVJKd1RpUU41aFZtcXZuM01rbGsybGxUdlBDYWVNUGJ1OWx4akVVaWRzQTU0bnAwNW8wZE5XVmc2ZHFzM21vSXFjbFpzVUxIR3ZNQXpsSDRURGd3eW1UajZ3VzZkMWI1MlYiLCJtYWMiOiJmNDlkMTU3NjAxZGJhM2MwMDBkOTI0YTJiNWI4YTQ0ZWRlNjI4MzVlODNlZmUwMWY4Y2VmOTNmZjQ0Yjc3NzYyIiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6ImdyRTVsb2srcWZoMUZub014MncxSlE9PSIsInZhbHVlIjoiZkpQOFo3Y2NwQmgvRUQwaTJrcDZQeHdwYkNUcVdJNXhLblhMdkttQzVIM2V2MWlOR2VzdjV3VXVvT1RoZHZmZGJydnRraUV1MTYySDNwdWU5WlZ4SXYyVTkxUENyc0kzWGE4MW1QVDZ6LzBQY2VJTkhxYWZEdzU3a0pjdG14TksiLCJtYWMiOiI3N2U3OWNmNjVjYzE2MzE1NWIzNmM3M2FjODRkNmYxOGIyOTVmYTY1MzlhNTdiZmE1ZDg2ZjEyOTYwZjYyZGEzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1985368072\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1336516536 data-indent-pad=\"  \"><span class=sf-dump-note>array:37</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"35 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">52575</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"6 characters\">/login</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"45 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"6 characters\">/login</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"16 characters\">/index.php/login</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"2 characters\">84</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"2 characters\">84</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlRxZGU1YjJ5eitRclllSlAzVVJXZlE9PSIsInZhbHVlIjoiWEhGczZHQk52UXhJTnNTSzZmc2psMVFJcjBuTG0ycVJKd1RpUU41aFZtcXZuM01rbGsybGxUdlBDYWVNUGJ1OWx4akVVaWRzQTU0bnAwNW8wZE5XVmc2ZHFzM21vSXFjbFpzVUxIR3ZNQXpsSDRURGd3eW1UajZ3VzZkMWI1MlYiLCJtYWMiOiJmNDlkMTU3NjAxZGJhM2MwMDBkOTI0YTJiNWI4YTQ0ZWRlNjI4MzVlODNlZmUwMWY4Y2VmOTNmZjQ0Yjc3NzYyIiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6ImdyRTVsb2srcWZoMUZub014MncxSlE9PSIsInZhbHVlIjoiZkpQOFo3Y2NwQmgvRUQwaTJrcDZQeHdwYkNUcVdJNXhLblhMdkttQzVIM2V2MWlOR2VzdjV3VXVvT1RoZHZmZGJydnRraUV1MTYySDNwdWU5WlZ4SXYyVTkxUENyc0kzWGE4MW1QVDZ6LzBQY2VJTkhxYWZEdzU3a0pjdG14TksiLCJtYWMiOiI3N2U3OWNmNjVjYzE2MzE1NWIzNmM3M2FjODRkNmYxOGIyOTVmYTY1MzlhNTdiZmE1ZDg2ZjEyOTYwZjYyZGEzIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753344478.3435</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753344478</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1336516536\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-348978625 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NYEHn10iGV9qMVXxamnzO9bZQWyfAnf8NbmLax09</span>\"\n  \"<span class=sf-dump-key>scolarite_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7h4EznLDsN5gIZ2EBc5JZT0REVbvD9O2KeZaKpuk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-348978625\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1123554852 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 24 Jul 2025 08:08:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InE5b1lDZW9WZ0p1OWRRNFU1NmlhSHc9PSIsInZhbHVlIjoiNFVPdFA4c0tMbkFxbjYyZ1hyeXZ2VWlnd2pLSDhkNzRVQ3BGUHMvT0QzcENLRk9ZSitlOGJ6SEs3LzZQMWptTUJzcC9aODBmOXJpczlsMmhJaGRCQkU4TXhuUjMxclZBR3dzcWxxRzNZaUdwWEVkcDNOemN2cVpzZGRyRG94V2UiLCJtYWMiOiI3YjY1OWY0YzE1MzRjZjRhZTczZjlkYmFhYTQxZDE5MThmODFhODI4NzhjOTVmMmMxODUwZjY3OTc4ZGM5ZDExIiwidGFnIjoiIn0%3D; expires=Thu, 24 Jul 2025 10:08:11 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"451 characters\">scolarite_imsaa_session=eyJpdiI6Ik5idjRia2hiYVBycytUSWgrTUlwNGc9PSIsInZhbHVlIjoiRlkrWXZYc0pqM0RWeGxSN3pEM1htdExWUi9yazFjcG9RSGsrQzdYbVcxYm5LczZsbTM3ZjJQQjcxdHVrRnNWSFBoVnJYbmdoajBaUDZPSXl3SnVuUUNmSXpuMTVsN2pMTE1rNUV2cHBZeVYxNHpFZ0laODZZOHA2aFVlZGtUM28iLCJtYWMiOiJmZjEzYjNhMTVmNDg0ZGE2NWQ1Njk3YTcxMzkzZWFkNmIxZmQ3Y2FiMjljNWU4MzcyNDU0Y2Q2MGQ4MGM4Y2FlIiwidGFnIjoiIn0%3D; expires=Thu, 24 Jul 2025 10:08:11 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InE5b1lDZW9WZ0p1OWRRNFU1NmlhSHc9PSIsInZhbHVlIjoiNFVPdFA4c0tMbkFxbjYyZ1hyeXZ2VWlnd2pLSDhkNzRVQ3BGUHMvT0QzcENLRk9ZSitlOGJ6SEs3LzZQMWptTUJzcC9aODBmOXJpczlsMmhJaGRCQkU4TXhuUjMxclZBR3dzcWxxRzNZaUdwWEVkcDNOemN2cVpzZGRyRG94V2UiLCJtYWMiOiI3YjY1OWY0YzE1MzRjZjRhZTczZjlkYmFhYTQxZDE5MThmODFhODI4NzhjOTVmMmMxODUwZjY3OTc4ZGM5ZDExIiwidGFnIjoiIn0%3D; expires=Thu, 24-Jul-2025 10:08:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"423 characters\">scolarite_imsaa_session=eyJpdiI6Ik5idjRia2hiYVBycytUSWgrTUlwNGc9PSIsInZhbHVlIjoiRlkrWXZYc0pqM0RWeGxSN3pEM1htdExWUi9yazFjcG9RSGsrQzdYbVcxYm5LczZsbTM3ZjJQQjcxdHVrRnNWSFBoVnJYbmdoajBaUDZPSXl3SnVuUUNmSXpuMTVsN2pMTE1rNUV2cHBZeVYxNHpFZ0laODZZOHA2aFVlZGtUM28iLCJtYWMiOiJmZjEzYjNhMTVmNDg0ZGE2NWQ1Njk3YTcxMzkzZWFkNmIxZmQ3Y2FiMjljNWU4MzcyNDU0Y2Q2MGQ4MGM4Y2FlIiwidGFnIjoiIn0%3D; expires=Thu, 24-Jul-2025 10:08:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1123554852\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-474483549 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VtnJlwxOpd0hRXXY8oKOSLlbl0TFeAO2LM004ptd</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>**********</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-474483549\", {\"maxDepth\":0})</script>\n"}}