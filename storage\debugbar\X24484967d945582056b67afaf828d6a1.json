{"__meta": {"id": "X24484967d945582056b67afaf828d6a1", "datetime": "2025-07-24 11:24:14", "utime": 1753345454.51609, "method": "POST", "uri": "/livewire/message/liste-new-etu", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753345452.681676, "end": 1753345454.516122, "duration": 1.8344461917877197, "duration_str": "1.83s", "measures": [{"label": "Booting", "start": 1753345452.681676, "relative_start": 0, "end": 1753345453.362752, "relative_end": 1753345453.362752, "duration": 0.6810760498046875, "duration_str": "681ms", "params": [], "collector": null}, {"label": "Application", "start": 1753345453.363608, "relative_start": 0.6819319725036621, "end": 1753345454.516125, "relative_end": 2.86102294921875e-06, "duration": 1.1525170803070068, "duration_str": "1.15s", "params": [], "collector": null}]}, "memory": {"peak_usage": 27886952, "peak_usage_str": "27MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 3, "templates": [{"name": "livewire.administration.contactEtu.index (\\resources\\views\\livewire\\administration\\contactEtu\\index.blade.php)", "param_count": 27, "params": ["etudiants", "parcourse", "niveaux", "annees", "livewireLayout", "errors", "_instance", "currentPage", "filtreParcours", "filtre<PERSON><PERSON>au", "filtreAnnee", "query", "perPage", "isLoading", "sortField", "sortDirection", "showFilters", "compactView", "totalEtudiants", "totalParParcours", "newEtus", "etus", "current_parcours", "current_niveau", "current_annee", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/administration/contactEtu/index.blade.php&line=0"}, {"name": "livewire.administration.contactEtu.liste (\\resources\\views\\livewire\\administration\\contactEtu\\liste.blade.php)", "param_count": 29, "params": ["__env", "app", "errors", "_instance", "etudiants", "parcourse", "niveaux", "annees", "livewireLayout", "currentPage", "filtreParcours", "filtre<PERSON><PERSON>au", "filtreAnnee", "query", "perPage", "isLoading", "sortField", "sortDirection", "showFilters", "compactView", "totalEtudiants", "totalParParcours", "newEtus", "etus", "current_parcours", "current_niveau", "current_annee", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/administration/contactEtu/liste.blade.php&line=0"}, {"name": "livewire::bootstrap (\\vendor\\livewire\\livewire\\src\\views\\pagination\\bootstrap.blade.php)", "param_count": 2, "params": ["paginator", "elements"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\vendor\\livewire\\livewire\\src\\views\\pagination/bootstrap.blade.php&line=0"}]}, "route": {"uri": "POST livewire/message/{name}", "uses": "Livewire\\Controllers\\HttpConnectionHandler@__invoke", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "as": "livewire.message", "middleware": "web"}, "queries": {"nb_statements": 14, "nb_failed_statements": 0, "accumulated_duration": 0.73727, "accumulated_duration_str": "737ms", "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.006860000000000001, "duration_str": "6.86ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:59", "connection": "imsaaapp", "start_percent": 0, "width_percent": 0.93}, {"sql": "select count(*) as aggregate from `users` where exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `role_id` = 5) and exists (select * from `inscription_students` where `users`.`id` = `inscription_students`.`user_id` and `annee_universitaire_id` = '5' and `inscription_students`.`deleted_at` is null) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 120}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 61}, {"index": 17, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php", "line": 81}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php", "line": 28}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\PerformDataBindingUpdates.php", "line": 20}], "duration": 0.037899999999999996, "duration_str": "37.9ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:120", "connection": "imsaaapp", "start_percent": 0.93, "width_percent": 5.141}, {"sql": "select * from `users` where exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `role_id` = 5) and exists (select * from `inscription_students` where `users`.`id` = `inscription_students`.`user_id` and `annee_universitaire_id` = '5' and `inscription_students`.`deleted_at` is null) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 129}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 61}, {"index": 16, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php", "line": 81}, {"index": 17, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php", "line": 28}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\PerformDataBindingUpdates.php", "line": 20}], "duration": 0.02468, "duration_str": "24.68ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:129", "connection": "imsaaapp", "start_percent": 6.071, "width_percent": 3.347}, {"sql": "select * from `inscription_students` where `inscription_students`.`user_id` in (12, 13, 14, 16, 17, 27, 28, 29, 39, 40, 41, 42, 43, 45, 46, 47, 48, 52, 53, 56, 57, 59, 66, 69, 71, 72, 73, 75, 77, 80, 85, 88, 91, 92, 93, 94, 95, 96, 98, 99, 100, 103, 104, 106, 107, 109, 110, 111, 112, 113, 114, 120, 130, 131, 132, 133, 139, 144, 185, 188, 189, 192, 193, 194, 195, 198, 199, 200, 201, 203, 204, 207, 208, 213, 215, 216, 217, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 240, 241, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 275, 276, 277, 278, 279, 281, 283, 284, 285, 286, 288, 289, 290, 291, 293, 294, 295, 299, 300, 301, 303, 304, 305, 306, 337, 338, 340, 342, 344, 345, 346, 347, 349, 351, 352, 354, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 367, 368, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 383, 385, 387, 388, 389, 390, 392, 393, 394, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426, 437, 438, 439, 440) and `inscription_students`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 129}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 61}, {"index": 21, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php", "line": 81}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php", "line": 28}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\PerformDataBindingUpdates.php", "line": 20}], "duration": 0.03821, "duration_str": "38.21ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:129", "connection": "imsaaapp", "start_percent": 9.419, "width_percent": 5.183}, {"sql": "select * from `parcours` where `parcours`.`id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 15, 17, 19, 21, 22, 24) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 129}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 61}, {"index": 26, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php", "line": 81}, {"index": 27, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php", "line": 28}, {"index": 28, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\PerformDataBindingUpdates.php", "line": 20}], "duration": 0.11053, "duration_str": "111ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:129", "connection": "imsaaapp", "start_percent": 14.601, "width_percent": 14.992}, {"sql": "select count(*) as aggregate from `users` where exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `role_id` = 5) and exists (select * from `inscription_students` where `users`.`id` = `inscription_students`.`user_id` and `annee_universitaire_id` = '4' and `inscription_students`.`deleted_at` is null) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 174}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.09669, "duration_str": "96.69ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:174", "connection": "imsaaapp", "start_percent": 29.593, "width_percent": 13.115}, {"sql": "select * from `users` where exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `role_id` = 5) and exists (select * from `inscription_students` where `users`.`id` = `inscription_students`.`user_id` and `annee_universitaire_id` = '4' and `inscription_students`.`deleted_at` is null) and `users`.`deleted_at` is null order by `nom` asc limit 15 offset 0", "type": "query", "params": [], "bindings": ["5", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 174}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.08843000000000001, "duration_str": "88.43ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:174", "connection": "imsaaapp", "start_percent": 42.708, "width_percent": 11.994}, {"sql": "select * from `inscription_students` where `inscription_students`.`user_id` in (7, 12, 16, 62, 103, 114, 185, 217, 233, 260, 262, 263, 272, 327, 334) and `annee_universitaire_id` = '4' and `inscription_students`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 174}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00796, "duration_str": "7.96ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:174", "connection": "imsaaapp", "start_percent": 54.702, "width_percent": 1.08}, {"sql": "select `id`, `sigle`, `nom` from `parcours` where `parcours`.`id` in (1, 2, 4, 5, 6, 10) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 174}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00478, "duration_str": "4.78ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:174", "connection": "imsaaapp", "start_percent": 55.781, "width_percent": 0.648}, {"sql": "select `id`, `nom` from `niveaux` where `niveaux`.`id` in (1, 2, 3) and `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 174}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.032159999999999994, "duration_str": "32.16ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:174", "connection": "imsaaapp", "start_percent": 56.43, "width_percent": 4.362}, {"sql": "select `id`, `nom` from `annee_universitaires` where `annee_universitaires`.`id` in (4) and `annee_universitaires`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 174}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.12303, "duration_str": "123ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:174", "connection": "imsaaapp", "start_percent": 60.792, "width_percent": 16.687}, {"sql": "select * from `parcours` where `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 178}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.09228, "duration_str": "92.28ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:178", "connection": "imsaaapp", "start_percent": 77.479, "width_percent": 12.516}, {"sql": "select * from `niveaux` where `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 179}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0286, "duration_str": "28.6ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:179", "connection": "imsaaapp", "start_percent": 89.996, "width_percent": 3.879}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`deleted_at` is null order by `nom` desc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 180}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.04516, "duration_str": "45.16ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:180", "connection": "imsaaapp", "start_percent": 93.875, "width_percent": 6.125}]}, "models": {"data": {"App\\Models\\AnneeUniversitaire": 7, "App\\Models\\Niveau": 8, "App\\Models\\Parcour": 49, "App\\Models\\InscriptionStudent": 547, "App\\Models\\User": 241}, "count": 852}, "livewire": {"data": {"liste-new-etu #bSj1DTIMuQEsLxmYlGr9": "array:5 [\n  \"data\" => array:20 [\n    \"currentPage\" => \"liste\"\n    \"filtreParcours\" => \"\"\n    \"filtreNiveau\" => \"\"\n    \"filtreAnnee\" => \"4\"\n    \"query\" => \"\"\n    \"perPage\" => 15\n    \"isLoading\" => false\n    \"sortField\" => \"nom\"\n    \"sortDirection\" => \"asc\"\n    \"showFilters\" => true\n    \"compactView\" => false\n    \"totalEtudiants\" => 225\n    \"totalParParcours\" => array:11 [\n      \"THR\" => 36\n      \"CM\" => 26\n      \"GFC\" => 28\n      \"TBA\" => 12\n      \"TD\" => 77\n      \"GBC\" => 15\n      \"GPM\" => 7\n      \"GEE\" => 14\n      \"GRT\" => 8\n      \"MMS\" => 1\n      \"ADA\" => 1\n    ]\n    \"newEtus\" => []\n    \"etus\" => []\n    \"current_parcours\" => null\n    \"current_niveau\" => null\n    \"current_annee\" => null\n    \"page\" => 1\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"liste-new-etu\"\n  \"view\" => \"livewire.administration.contactEtu.index\"\n  \"component\" => \"App\\Http\\Livewire\\ListeNewEtu\"\n  \"id\" => \"bSj1DTIMuQEsLxmYlGr9\"\n]"}, "count": 1}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VtnJlwxOpd0hRXXY8oKOSLlbl0TFeAO2LM004ptd", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/caisse/infoetus\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1753344491\n]"}, "request": {"path_info": "/livewire/message/liste-new-etu", "status_code": "<pre class=sf-dump id=sf-dump-2044359114 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2044359114\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1574713696 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1574713696\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-696979811 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">bSj1DTIMuQEsLxmYlGr9</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">liste-new-etu</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"15 characters\">caisse/infoetus</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => []\n    \"<span class=sf-dump-key>errors</span>\" => []\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">d98475fc</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:20</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>currentPage</span>\" => \"<span class=sf-dump-str title=\"5 characters\">liste</span>\"\n      \"<span class=sf-dump-key>filtreParcours</span>\" => \"\"\n      \"<span class=sf-dump-key>filtreNiveau</span>\" => \"\"\n      \"<span class=sf-dump-key>filtreAnnee</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>query</span>\" => \"\"\n      \"<span class=sf-dump-key>perPage</span>\" => <span class=sf-dump-num>15</span>\n      \"<span class=sf-dump-key>isLoading</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>sortField</span>\" => \"<span class=sf-dump-str title=\"3 characters\">nom</span>\"\n      \"<span class=sf-dump-key>sortDirection</span>\" => \"<span class=sf-dump-str title=\"3 characters\">asc</span>\"\n      \"<span class=sf-dump-key>showFilters</span>\" => <span class=sf-dump-const>true</span>\n      \"<span class=sf-dump-key>compactView</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>totalEtudiants</span>\" => <span class=sf-dump-num>232</span>\n      \"<span class=sf-dump-key>totalParParcours</span>\" => <span class=sf-dump-note>array:14</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>MAT</span>\" => <span class=sf-dump-num>2</span>\n        \"<span class=sf-dump-key>THR</span>\" => <span class=sf-dump-num>37</span>\n        \"<span class=sf-dump-key>CM</span>\" => <span class=sf-dump-num>18</span>\n        \"<span class=sf-dump-key>GFC</span>\" => <span class=sf-dump-num>17</span>\n        \"<span class=sf-dump-key>TBA</span>\" => <span class=sf-dump-num>11</span>\n        \"<span class=sf-dump-key>TD</span>\" => <span class=sf-dump-num>64</span>\n        \"<span class=sf-dump-key>GBC</span>\" => <span class=sf-dump-num>11</span>\n        \"<span class=sf-dump-key>GPM</span>\" => <span class=sf-dump-num>5</span>\n        \"<span class=sf-dump-key>GEE</span>\" => <span class=sf-dump-num>18</span>\n        \"<span class=sf-dump-key>GRT</span>\" => <span class=sf-dump-num>13</span>\n        \"<span class=sf-dump-key>LCI</span>\" => <span class=sf-dump-num>24</span>\n        \"<span class=sf-dump-key>ISE</span>\" => <span class=sf-dump-num>5</span>\n        \"<span class=sf-dump-key>ADA</span>\" => <span class=sf-dump-num>6</span>\n        \"<span class=sf-dump-key>ICA</span>\" => <span class=sf-dump-num>1</span>\n      </samp>]\n      \"<span class=sf-dump-key>newEtus</span>\" => []\n      \"<span class=sf-dump-key>etus</span>\" => []\n      \"<span class=sf-dump-key>current_parcours</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>current_niveau</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>current_annee</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>paginators</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => []\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">0e65e430306d73be36e7ab1e3f3af7c725c83e2d5d0824ae37ff5e3171273b37</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"9 characters\">syncInput</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">a02s</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">filtreAnnee</span>\"\n        \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str>4</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-696979811\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-613794937 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">869</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">VtnJlwxOpd0hRXXY8oKOSLlbl0TFeAO2LM004ptd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/caisse/infoetus</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkhmaGhBd2JjSlhiUFh6VUlWKzEvUFE9PSIsInZhbHVlIjoiQ0dkTzVtMGZuNFlpZmQ4MGxnNWcwK0pLaElrV3dEazdzOW9LdDF5RFlWWHB5NGcxT0lMcnpkV3Nab2NWMTZvekgvWHFScEZDYVB0bHhycURsNm0yZDltUE5udUJJT2ZqeVBUcHh3clRmaXNvZERDcDJQeWZSbDM3QkU3bVFCb0MiLCJtYWMiOiI4M2YwYmZhY2Y4YzljMGI0NWJmN2Q1MzE0YmRhNWNhNmJkMjJlOGJjZDk0NWRiYzAyYmZmYjRjNTdhY2YxMjAzIiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6ImUxQm9oZHdHdTNsODFDK1JWTHk5NlE9PSIsInZhbHVlIjoiMnZuTTg0RTVKWE50YXVsdmxraWd2eHBkTExZRHRSQjI3N0F2NHNDeGtHeHM4MHR2SDI3RE5nZUsrQVNuQnh1ZEtFMjJFejhzTy9LejREeGZIK2FkOGlVNWpOekhqU0RPV3IrL0trOGdNY0xPNTNyM1NibkZTeDc2aFhIc2dGck0iLCJtYWMiOiI1YWM0YmIwMmIxYTNkYWMzN2QwY2NkYmY5NzA1NzM4OGFlMTg2YjViNGMwYjdhNGY0MTEwZjQ0MGYxNjBiNTBkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-613794937\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1575818718 data-indent-pad=\"  \"><span class=sf-dump-note>array:36</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"35 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">53563</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"31 characters\">/livewire/message/liste-new-etu</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"45 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"31 characters\">/livewire/message/liste-new-etu</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"41 characters\">/index.php/livewire/message/liste-new-etu</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">869</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">869</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_X_LIVEWIRE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/caisse/infoetus</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkhmaGhBd2JjSlhiUFh6VUlWKzEvUFE9PSIsInZhbHVlIjoiQ0dkTzVtMGZuNFlpZmQ4MGxnNWcwK0pLaElrV3dEazdzOW9LdDF5RFlWWHB5NGcxT0lMcnpkV3Nab2NWMTZvekgvWHFScEZDYVB0bHhycURsNm0yZDltUE5udUJJT2ZqeVBUcHh3clRmaXNvZERDcDJQeWZSbDM3QkU3bVFCb0MiLCJtYWMiOiI4M2YwYmZhY2Y4YzljMGI0NWJmN2Q1MzE0YmRhNWNhNmJkMjJlOGJjZDk0NWRiYzAyYmZmYjRjNTdhY2YxMjAzIiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6ImUxQm9oZHdHdTNsODFDK1JWTHk5NlE9PSIsInZhbHVlIjoiMnZuTTg0RTVKWE50YXVsdmxraWd2eHBkTExZRHRSQjI3N0F2NHNDeGtHeHM4MHR2SDI3RE5nZUsrQVNuQnh1ZEtFMjJFejhzTy9LejREeGZIK2FkOGlVNWpOekhqU0RPV3IrL0trOGdNY0xPNTNyM1NibkZTeDc2aFhIc2dGck0iLCJtYWMiOiI1YWM0YmIwMmIxYTNkYWMzN2QwY2NkYmY5NzA1NzM4OGFlMTg2YjViNGMwYjdhNGY0MTEwZjQ0MGYxNjBiNTBkIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753345452.6817</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753345452</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1575818718\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1238741181 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VtnJlwxOpd0hRXXY8oKOSLlbl0TFeAO2LM004ptd</span>\"\n  \"<span class=sf-dump-key>scolarite_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RKkczZOcD1TSWkk2eKq2gL5rBSt8JPLoI0Me9VkZ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1238741181\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1902961491 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 24 Jul 2025 08:24:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IktxTHQ5TzdUd29XejlOTFpReVAySVE9PSIsInZhbHVlIjoiOTRta1JnNUNYa241WEZ0MHZjdmdESCt6UjhPZjJWeUdNMXZVQnBNR0ZDcnA0MlFGc0UvTUNETGtrZ1FxamNvL0xIQzJaZTFESkprVG0weDhjcDBPZncxOTYweDhtREFycldicGJpVlBGZ1NCY0hGWWUvcEJ4ZThQaEdjcnl3YmUiLCJtYWMiOiIxZTYyZDI2NGI1NDlkNjU2ZGZlZjNjYmViYWQxYjk3MGE0NjNkZWU1MjNmODdhOWM4YzY2YWYyMDhmMzNhODQ4IiwidGFnIjoiIn0%3D; expires=Thu, 24 Jul 2025 10:24:14 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"451 characters\">scolarite_imsaa_session=eyJpdiI6Ilpoa2hkK3lPd1o4SlBjT1QvL1ZMS1E9PSIsInZhbHVlIjoiSG9FK0kyWHI0dHlKS1FmdW1oTmlKWUI3ekxyOGVzcmlSYlE4LzkzNndWZlZVNFhINUxJdkxTdTJSM3NJbnAya1Ric2ZkVTBiekM1OWJSR3QvRnp1SERLQmNQYWo4YlFRR2hpTU82QUVjOHN3SFJZWFdLdnk2NnA0VityWktzVGIiLCJtYWMiOiI0Y2RlYTMwNDhhZWM2ODg3OWJmNTY5ODkzOWJlZjQwYzUwNmNmYmFjMzM5ODY2OWNhMmIzYTM4NTc3ZDhhNGU4IiwidGFnIjoiIn0%3D; expires=Thu, 24 Jul 2025 10:24:14 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IktxTHQ5TzdUd29XejlOTFpReVAySVE9PSIsInZhbHVlIjoiOTRta1JnNUNYa241WEZ0MHZjdmdESCt6UjhPZjJWeUdNMXZVQnBNR0ZDcnA0MlFGc0UvTUNETGtrZ1FxamNvL0xIQzJaZTFESkprVG0weDhjcDBPZncxOTYweDhtREFycldicGJpVlBGZ1NCY0hGWWUvcEJ4ZThQaEdjcnl3YmUiLCJtYWMiOiIxZTYyZDI2NGI1NDlkNjU2ZGZlZjNjYmViYWQxYjk3MGE0NjNkZWU1MjNmODdhOWM4YzY2YWYyMDhmMzNhODQ4IiwidGFnIjoiIn0%3D; expires=Thu, 24-Jul-2025 10:24:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"423 characters\">scolarite_imsaa_session=eyJpdiI6Ilpoa2hkK3lPd1o4SlBjT1QvL1ZMS1E9PSIsInZhbHVlIjoiSG9FK0kyWHI0dHlKS1FmdW1oTmlKWUI3ekxyOGVzcmlSYlE4LzkzNndWZlZVNFhINUxJdkxTdTJSM3NJbnAya1Ric2ZkVTBiekM1OWJSR3QvRnp1SERLQmNQYWo4YlFRR2hpTU82QUVjOHN3SFJZWFdLdnk2NnA0VityWktzVGIiLCJtYWMiOiI0Y2RlYTMwNDhhZWM2ODg3OWJmNTY5ODkzOWJlZjQwYzUwNmNmYmFjMzM5ODY2OWNhMmIzYTM4NTc3ZDhhNGU4IiwidGFnIjoiIn0%3D; expires=Thu, 24-Jul-2025 10:24:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1902961491\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1359735330 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VtnJlwxOpd0hRXXY8oKOSLlbl0TFeAO2LM004ptd</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/caisse/infoetus</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1753344491</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1359735330\", {\"maxDepth\":0})</script>\n"}}