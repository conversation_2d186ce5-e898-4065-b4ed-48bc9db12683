<!-- Hero avec statistiques -->
<div class="bg-body-light">
    <div class="content content-full">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <div>
                <h1 class="h3 fw-bold mb-1">Contacts des Étudiants</h1>
                <div class="d-flex gap-3 text-muted fs-sm">
                    <span><i class="fa fa-users me-1"></i> <?php echo e($totalEtudiants); ?> étudiants</span>
                    <?php if($query || $filtreParcours || $filtreNiveau || $filtreAnnee): ?>
                        <span class="badge bg-info">Filtres actifs</span>
                    <?php endif; ?>
                </div>
            </div>
            <div class="d-flex gap-2">
                <button type="button" class="btn btn-sm btn-outline-primary" wire:click="toggleFilters">
                    <i class="fa fa-filter me-1"></i>
                    <?php echo e($showFilters ? 'Masquer' : 'Afficher'); ?> filtres
                </button>
                <button type="button" class="btn btn-sm btn-outline-secondary" wire:click="toggleCompactView">
                    <i class="fa fa-<?php echo e($compactView ? 'expand' : 'compress'); ?> me-1"></i>
                    Vue <?php echo e($compactView ? 'détaillée' : 'compacte'); ?>

                </button>
                <?php if($totalEtudiants > 0): ?>
                    <button type="button" class="btn btn-sm btn-primary" wire:click="goToEtat">
                        <i class="fa fa-print me-1"></i> Générer PDF
                    </button>
                <?php endif; ?>
            </div>
        </div>

        <!-- Statistiques par parcours -->
        <?php if(count($totalParParcours) > 0): ?>
            <div class="row g-2 mb-3">
                <?php $__currentLoopData = $totalParParcours; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $parcours => $count): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="col-auto">
                        <span class="badge bg-secondary"><?php echo e($parcours); ?>: <?php echo e($count); ?></span>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        <?php endif; ?>
    </div>
</div>
<!-- END Hero -->

<!-- Page Content -->
<div class="content">
    <!-- Filtres avancés -->
    <?php if($showFilters): ?>
        <div class="block block-rounded mb-3">
            <div class="block-header block-header-default">
                <h3 class="block-title">
                    <i class="fa fa-filter me-2"></i>Filtres de recherche
                </h3>
                <div class="block-options">
                    <button type="button" class="btn btn-sm btn-outline-danger" wire:click="clearAllFilters">
                        <i class="fa fa-times me-1"></i> Effacer tout
                    </button>
                </div>
            </div>
            <div class="block-content">
                <div class="row g-3">
                    <!-- Recherche textuelle -->
                    <div class="col-md-4">
                        <label class="form-label">Recherche</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fa fa-search"></i>
                            </span>
                            <input type="text" class="form-control" wire:model.debounce.300ms="query"
                                placeholder="Nom, prénom, matricule, téléphone...">
                            <?php if($query): ?>
                                <button class="btn btn-outline-secondary" type="button" wire:click="$set('query', '')">
                                    <i class="fa fa-times"></i>
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Filtre Année -->
                    <div class="col-md-2">
                        <label class="form-label">Année Universitaire</label>
                        <select class="form-select" wire:model="filtreAnnee">
                            <option value="">Toutes les années</option>
                            <?php $__currentLoopData = $annees; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $annee): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($annee->id); ?>"><?php echo e($annee->nom); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>

                    <!-- Filtre Parcours -->
                    <div class="col-md-3">
                        <label class="form-label">Parcours</label>
                        <select class="form-select" wire:model="filtreParcours">
                            <option value="">Tous les parcours</option>
                            <?php $__currentLoopData = $parcourse; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $parcour): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($parcour->id); ?>"><?php echo e($parcour->sigle); ?> - <?php echo e($parcour->nom); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>

                    <!-- Filtre Niveau -->
                    <div class="col-md-2">
                        <label class="form-label">Niveau</label>
                        <select class="form-select" wire:model="filtreNiveau">
                            <option value="">Tous les niveaux</option>
                            <?php $__currentLoopData = $niveaux; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $niveau): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($niveau->id); ?>"><?php echo e($niveau->nom); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>

                    <!-- Nombre par page -->
                    <div class="col-md-1">
                        <label class="form-label">Par page</label>
                        <select class="form-select" wire:model="perPage">
                            <option value="10">10</option>
                            <option value="15">15</option>
                            <option value="25">25</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Liste des étudiants -->
    <div class="block block-rounded">
        <div class="block-header block-header-default">
            <h3 class="block-title">
                <i class="fa fa-address-book me-2"></i>Liste des contacts
                <?php if($totalEtudiants > 0): ?>
                    <span class="badge bg-primary ms-2"><?php echo e($totalEtudiants); ?></span>
                <?php endif; ?>
            </h3>
            <div class="block-options">
                <?php if($isLoading): ?>
                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <div class="block-content block-content-full">
            <?php if($etudiants->count() > 0): ?>
                <!-- Table responsive -->
                <div class="table-responsive">
                    <table
                        class="table table-bordered table-striped table-vcenter <?php echo e($compactView ? 'table-sm' : ''); ?>">
                        <thead class="table-dark">
                            <tr>
                                <?php if (! ($compactView)): ?>
                                    <th class="text-center" style="width: 50px;">#</th>
                                    <th class="text-center">
                                    <i class="fa fa-phone me-1"></i>Téléphone
                                </th>
                                <?php endif; ?>
                                
                                <th wire:click="sortBy('nom')" class="cursor-pointer">
                                    <i class="fa fa-user me-1"></i>Nom
                                    <?php if($sortField === 'nom'): ?>
                                        <i class="fa fa-sort-<?php echo e($sortDirection === 'asc' ? 'up' : 'down'); ?> ms-1"></i>
                                    <?php endif; ?>
                                </th>
                                <th class="text-center">
                                    <i class="fa fa-phone me-1"></i>Prénom
                                </th>
                                <?php if($compactView): ?>
                                <th class="text-center">
                                    <i class="fa fa-birthday-cake me-1"></i>Date de naissance
                                </th>
                                <th class="text-center">
                                    <i class="fa fa-map-marker-alt me-1"></i>Lieu de naissance
                                </th>
                                <th class="text-center">
                                    <i class="fa fa-home me-1"></i>Adresse
                                </th>
                                <?php endif; ?>
                                <th wire:click="sortBy('matricule')" class="cursor-pointer text-center">
                                    <i class="fa fa-id-card me-1"></i>Matricule
                                    <?php if($sortField === 'matricule'): ?>
                                        <i class="fa fa-sort-<?php echo e($sortDirection === 'asc' ? 'up' : 'down'); ?> ms-1"></i>
                                    <?php endif; ?>
                                </th>
                                <?php if($compactView): ?>
                                <th class="text-center">
                                    <i class="fa fa-graduation-cap me-1"></i>Domaine
                                </th>
                                <?php endif; ?>
                                <th class="text-center">
                                    <i class="fa fa-graduation-cap me-1"></i>Parcours
                                </th>
                                <th class="text-center">
                                    <i class="fa fa-layer-group me-1"></i>Niveau
                                </th>
                                <th class="text-center">
                                    <i class="fa fa-calendar me-1"></i>Année
                                </th>
                                <?php if (! ($compactView)): ?>
                                    <th class="text-center">
                                        <i class="fa fa-male me-1"></i>Nom du père
                                    </th>
                                    <th class="text-center">
                                        <i class="fa fa-female me-1"></i>Nom de la mère
                                    </th>
                                <?php endif; ?>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $etudiants; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $etudiant): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php
                                    $inscription = $etudiant->info->first();
                                ?>
                                <tr>
                                    <?php if (! ($compactView)): ?>
                                        <td class="text-center fw-bold">
                                            <?php echo e($loop->iteration + ($etudiants->currentPage() - 1) * $etudiants->perPage()); ?>

                                        </td>
                                        <td class="text-center">
                                            <div class="fw-semibold text-primary"><?php echo e($etudiant->telephone1 ?: 'N/A'); ?></div>
                                            <?php if($etudiant->telephone2): ?>
                                                <small class="text-muted"><?php echo e($etudiant->telephone2); ?></small>
                                            <?php endif; ?>
                                        </td>
                                    <?php endif; ?>
                                    <td class="fw-semibold">
                                        <div class="d-flex align-items-center">
                                            <?php if (! ($compactView)): ?>
                                                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2"
                                                    style="width: 32px; height: 32px; font-size: 14px;">
                                                    <?php echo e(substr($etudiant->nom, 0, 1)); ?><?php echo e(substr($etudiant->prenom, 0, 1)); ?>

                                                </div>
                                            <?php endif; ?>
                                            <div>
                                                <div><?php echo e($etudiant->nom); ?></div>
                                                <?php if (! ($compactView)): ?>
                                                    <?php if($etudiant->sexe): ?>
                                                        <small class="text-muted">
                                                            <i
                                                                class="fa fa-<?php echo e($etudiant->sexe === 'M' ? 'male' : 'female'); ?> me-1"></i>
                                                            <?php echo e($etudiant->sexe === 'M' ? 'Masculin' : 'Féminin'); ?>

                                                        </small>
                                                    <?php endif; ?>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="">
                                        <div class="fw-semibold "><?php echo e($etudiant->prenom ?: ' '); ?></div>
                                    </td>
                                    <?php if($compactView): ?>
                                    <td class="text-center text-muted">
                                        <?php echo e($etudiant->date_naissance ? $etudiant->date_naissance : ' '); ?>

                                    </td>
                                    <td class="text-center text-muted"><?php echo e($etudiant->lieu_naissance ?: ' '); ?></td>
                                    <td class="text-center text-muted"><?php echo e($etudiant->adresse ?: ' '); ?></td>
                                    <?php endif; ?>

                                    <td class="text-center">
                                        <span class="badge bg-secondary"><?php echo e($etudiant->matricule ?: 'N/A'); ?></span>
                                    </td>
                                    <?php if($compactView): ?>
                                    <td class="text-center">
                                        <?php if($inscription && $inscription->parcours): ?>
                                            <span class="badge bg-info"><?php echo e($inscription->parcours->mention->domaine->nom); ?></span>
                                        <?php else: ?>
                                            <span class="badge bg-warning">Pas de domaine</span>
                                        <?php endif; ?>
                                    </td>
                                    <?php endif; ?>
                                    <td class="text-center">
                                        <?php if($inscription && $inscription->parcours): ?>
                                            <span class="badge bg-info"><?php echo e($inscription->parcours->sigle); ?></span>
                                            <?php if (! ($compactView)): ?>
                                                <div class="small text-muted mt-1"><?php echo e($inscription->parcours->nom); ?></div>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <span class="badge bg-warning">Pas de parcours</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-center">
                                        <?php if($inscription && $inscription->niveau): ?>
                                            <span class="badge bg-success"><?php echo e($inscription->niveau->nom); ?></span>
                                        <?php else: ?>
                                            <span class="badge bg-warning"> </span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-center">
                                        <?php if($inscription && $inscription->annee): ?>
                                            <span class="badge bg-primary"><?php echo e($inscription->annee->nom); ?></span>
                                        <?php else: ?>
                                            <span class="badge bg-warning"> </span>
                                        <?php endif; ?>
                                    </td>
                                    <?php if (! ($compactView)): ?>
                                        <td class="text-center text-muted"><?php echo e($etudiant->nom_pere ?: ' '); ?></td>
                                        <td class="text-center text-muted"><?php echo e($etudiant->nom_mere ?: ' '); ?></td>
                                    <?php endif; ?>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div class="text-muted">
                        Affichage de <?php echo e($etudiants->firstItem()); ?> à <?php echo e($etudiants->lastItem()); ?>

                        sur <?php echo e($etudiants->total()); ?> résultats
                    </div>
                    <div>
                        <?php echo e($etudiants->links()); ?>

                    </div>
                </div>
            <?php else: ?>
                <!-- État vide -->
                <div class="text-center py-5">
                    <div class="mb-3">
                        <i class="fa fa-users fa-3x text-muted"></i>
                    </div>
                    <h4 class="text-muted">Aucun étudiant trouvé</h4>
                    <p class="text-muted">
                        <?php if($query || $filtreParcours || $filtreNiveau || $filtreAnnee): ?>
                            Aucun étudiant ne correspond aux critères de recherche.
                            <br>
                            <button type="button" class="btn btn-outline-primary mt-2" wire:click="clearAllFilters">
                                <i class="fa fa-refresh me-1"></i> Effacer les filtres
                            </button>
                        <?php else: ?>
                            Aucun étudiant n'est enregistré dans le système.
                        <?php endif; ?>
                    </p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<!-- END Page Content -->

<style>
    .cursor-pointer {
        cursor: pointer;
    }

    .cursor-pointer:hover {
        background-color: rgba(0, 0, 0, 0.05);
    }
</style>
<?php /**PATH C:\xampp\htdocs\ImsaaProject\resources\views/livewire/administration/contactEtu/liste.blade.php ENDPATH**/ ?>