<div class="js-sidebar-scroll">
    <!-- Side Navigation -->
    <div class="content-side">
        <ul class="nav-main">
            <li class="nav-main-item">
                <a class="nav-main-link<?php echo e(request()->is('/') ? ' active' : ''); ?>" href="/">
                    <i class="nav-main-link-icon si si-cursor"></i>
                    <span class="nav-main-link-name">Accueil</span>
                </a>
            </li>

            <!-- lien pour admin -->
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('admin')): ?>
                <li class="nav-main-heading">Admin</li>
                <li class="nav-main-item<?php echo e(setMenuClass('admin.habilitations.', ' open')); ?>">
                    <a class="nav-main-link nav-main-link-submenu" data-toggle="submenu" aria-haspopup="true"
                        aria-expanded="true" href="#">
                        <i class="nav-main-link-icon si si-bulb"></i>
                        <span class="nav-main-link-name">Gestion</span>
                    </a>
                    <ul class="nav-main-submenu">
                        <li class="nav-main-item">
                            <a class="nav-main-link<?php echo e(setMenuClass('admin.habilitations.users.index', ' active')); ?>"
                                href="<?php echo e(route('admin.habilitations.users.index')); ?>">
                                <span class="nav-main-link-name">Utilisateurs</span>
                            </a>
                        </li>
                        <li class="nav-main-item">
                            <a class="nav-main-link<?php echo e(setMenuClass('admin.habilitations.figer.index', ' active')); ?>" href="<?php echo e(route('admin.habilitations.figer.index')); ?>">
                                <span class="nav-main-link-name">Figer Note</span>
                            </a>
                        </li>
                        <li class="nav-main-item">
                            <a class="nav-main-link<?php echo e(request()->is('pages/blank') ? ' active' : ''); ?>" href="/pages/blank">
                                <span class="nav-main-link-name">Blank</span>
                            </a>
                        </li>
                    </ul>
                </li>

                <li class="nav-main-item<?php echo e(setMenuClass('admin.caisse.', ' open')); ?>">
                    <a class="nav-main-link nav-main-link-submenu" data-toggle="submenu" aria-haspopup="true"
                        aria-expanded="true" href="#">
                        <i class="nav-main-link-icon si si-wallet"></i>
                        <span class="nav-main-link-name">CAISSE</span>
                    </a>
                    <ul class="nav-main-submenu">
                        <li class="nav-main-item">
                            <a class="nav-main-link<?php echo e(setMenuClass('admin.caisse.todaypayment.index', ' active')); ?>"
                                href="<?php echo e(route('admin.caisse.todaypayment.index')); ?>">
                                <span class="nav-main-link-name">Paiment du jour</span>
                            </a>
                        </li>
                        <li class="nav-main-item">
                            <a class="nav-main-link<?php echo e(setMenuClass('admin.caisse.historiquepayment.index', ' active')); ?>"
                                href="<?php echo e(route('admin.caisse.historiquepayment.index')); ?>">
                                <span class="nav-main-link-name">Historiques des paiments</span>
                            </a>
                        </li>
                        <li class="nav-main-item">
                            <a class="nav-main-link<?php echo e(setMenuClass('admin.caisse.typepay.index', ' active')); ?>"
                                href="<?php echo e(route('admin.caisse.typepay.index')); ?>">
                                <span class="nav-main-link-name">Gestion des types de paiments</span>
                            </a>
                        </li>
                        <li class="nav-main-item">
                            <a class="nav-main-link<?php echo e(setMenuClass('admin.caisse.voiretat.index', ' active')); ?>"
                                href="<?php echo e(route('admin.caisse.voiretat.index')); ?>">
                                <span class="nav-main-link-name">Voir les états de paiments</span>
                            </a>
                        </li>
                        <li class="nav-main-item">
                            <a class="nav-main-link<?php echo e(setMenuClass('admin.caisse.debt.index', ' active')); ?>"
                                href="<?php echo e(route('admin.caisse.debt.index')); ?>">
                                <span class="nav-main-link-name">Voir les paiments incomplets</span>
                            </a>
                        </li>
                        <li class="nav-main-item">
                            <a class="nav-main-link<?php echo e(setMenuClass('admin.caisse.infoetu.index', ' active')); ?>"
                                href="<?php echo e(route('admin.caisse.infoetu.index')); ?>">
                                <span class="nav-main-link-name">Information sur les étudiants</span>
                            </a>
                        </li>
                        <li class="nav-main-item">
                            <a class="nav-main-link<?php echo e(setMenuClass('admin.caisse.paystatus.index', ' active')); ?>"
                                href="<?php echo e(route('admin.caisse.paystatus.index')); ?>">
                                <span class="nav-main-link-name">Status des paiments</span>
                            </a>
                        </li>

                    </ul>
                </li>
            <?php endif; ?>

            <!-- lien pour enseignant -->
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('enseignant')): ?>
                <li class="nav-main-heading">Enseignant</li>

                <li class="nav-main-item">
                    <a class="nav-main-link<?php echo e(request()->is('pages/datatables') ? ' active' : ''); ?>"
                        href="/pages/datatables">
                        <span class="nav-main-link-name">Emploi du temps</span>
                    </a>
                </li>
                <li class="nav-main-item">
                    <a class="nav-main-link<?php echo e(setMenuClass('enseignant.gestions.cours.index', ' active')); ?>"
                        href="<?php echo e(route('enseignant.gestions.cours.index')); ?>">
                        <span class="nav-main-link-name">Mes Cours</span>
                    </a>
                </li>
            <?php endif; ?>

            <!-- lien pour deraq -->
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('deraq')): ?>
                <li class="nav-main-heading">D.E.R.A.Q</li>
                <li class="nav-main-item<?php echo e(setMenuClass('deraq.pedagogiques.', ' open')); ?>">
                    <a class="nav-main-link nav-main-link-submenu" data-toggle="submenu" aria-haspopup="true"
                        aria-expanded="true" href="#">
                        <i class="nav-main-link-icon si si-energy"></i>
                        <span class="nav-main-link-name">Services Pedagogiques</span>
                    </a>
                    <ul class="nav-main-submenu">
                    <li class="nav-main-item">
                            <a class="nav-main-link<?php echo e(setMenuClass('deraq.pedagogiques.annees.index', ' active')); ?>"
                                href="<?php echo e(route('deraq.pedagogiques.annees.index')); ?>">
                                <span class="nav-main-link-name">Gestion Années Universitaires</span>
                            </a>
                        </li>
                        
                        
                        <li class="nav-main-item">
                            <a class="nav-main-link<?php echo e(setMenuClass('deraq.pedagogiques.domaines.index', ' active')); ?>"
                                href="<?php echo e(route('deraq.pedagogiques.domaines.index')); ?>">
                                <span class="nav-main-link-name">Gestion Domaine</span>
                            </a>
                        </li>
                        
                        <li class="nav-main-item">
                            <a class="nav-main-link<?php echo e(setMenuClass('deraq.pedagogiques.semestre.index', ' active')); ?>"
                                href="<?php echo e(route('deraq.pedagogiques.semestre.index')); ?>">
                                <span class="nav-main-link-name">Gestion Semestre</span>
                            </a>
                        </li>
                        <li class="nav-main-item">
                            <a class="nav-main-link<?php echo e(setMenuClass('deraq.pedagogiques.mention.index', ' active')); ?>"
                                href="<?php echo e(route('deraq.pedagogiques.mention.index')); ?>">
                                <span class="nav-main-link-name">Gestion Mention et Parcours</span>
                            </a>
                        </li>
                        <li class="nav-main-item">
                            <a class="nav-main-link<?php echo e(setMenuClass('deraq.pedagogiques.ue.index', ' active')); ?>"
                                href="<?php echo e(route('deraq.pedagogiques.ue.index')); ?>">
                                <span class="nav-main-link-name">Gestion UE et EC</span>
                            </a>
                        </li>
                        <li class="nav-main-item">
                            <a class="nav-main-link<?php echo e(setMenuClass('deraq.pedagogiques.cours.index', ' active')); ?>"
                                href="<?php echo e(route('deraq.pedagogiques.cours.index')); ?>">
                                <span class="nav-main-link-name">Ajout Notes</span>
                            </a>
                        </li>
                        <li class="nav-main-item">
                            <a class="nav-main-link<?php echo e(setMenuClass('deraq.pedagogiques.history.index', ' active')); ?>"
                                href="<?php echo e(route('deraq.pedagogiques.history.index')); ?>">
                                <span class="nav-main-link-name">Gestion Historique des Notes</span>
                            </a>
                        </li>
                        <li class="nav-main-item">
                            <a class="nav-main-link<?php echo e(setMenuClass('deraq.pedagogiques.status.index', ' active')); ?>"
                                href="<?php echo e(route('deraq.pedagogiques.status.index')); ?>">
                                <span class="nav-main-link-name">Status de note</span>
                            </a>
                        </li>

                        <li class="nav-main-item">
                            <a class="nav-main-link<?php echo e(setMenuClass('deraq.pedagogiques.resultat.index', ' active')); ?>"
                                href="<?php echo e(route('deraq.pedagogiques.resultat.index')); ?>">
                                <span class="nav-main-link-name">Générer Résultat</span>
                            </a>
                        </li>
                        <li class="nav-main-item">
                            <a class="nav-main-link<?php echo e(setMenuClass('deraq.pedagogiques.rattrapage.index', ' active')); ?>"
                                href="<?php echo e(route('deraq.pedagogiques.rattrapage.index')); ?>">
                                <span class="nav-main-link-name">Générer liste de rattrapage</span>
                            </a>
                        </li>
                        <li class="nav-main-item">
                            <a class="nav-main-link<?php echo e(setMenuClass('deraq.pedagogiques.prometu.index', ' active')); ?>"
                                href="<?php echo e(route('deraq.pedagogiques.prometu.index')); ?>">
                                <span class="nav-main-link-name">Gestion d'admission</span>
                            </a>
                        </li>
                        <li class="nav-main-item">
                            <a class="nav-main-link<?php echo e(setMenuClass('deraq.pedagogiques.notes.index', ' active')); ?>"
                                href="<?php echo e(route('deraq.pedagogiques.notes.index')); ?>">
                                <span class="nav-main-link-name">Gestion des notes</span>
                            </a>
                        </li>
                    </ul>
                </li>

                <li class="nav-main-item<?php echo e(setMenuClass('deraq.gestions.', ' open')); ?>">
                    <a class="nav-main-link nav-main-link-submenu" data-toggle="submenu" aria-haspopup="true"
                        aria-expanded="true" href="#">
                        <i class="nav-main-link-icon si si-layers"></i>
                        <span class="nav-main-link-name">Gestion d'utilisateurs</span>
                    </a>
                    <ul class="nav-main-submenu">

                        <li class="nav-main-item">
                            <a class="nav-main-link<?php echo e(setMenuClass('deraq.gestions.enseignants.index', ' active')); ?>"
                                href="<?php echo e(route('deraq.gestions.enseignants.index')); ?>">
                                <span class="nav-main-link-name">Enseignant</span>
                            </a>
                        </li>
                        <li class="nav-main-item">
                            <a class="nav-main-link<?php echo e(setMenuClass('deraq.gestions.etudiants.index', ' active')); ?>"
                                href="<?php echo e(route('deraq.gestions.etudiants.index')); ?>">
                                <span class="nav-main-link-name">Etudiants</span>
                            </a>
                        </li>

                    </ul>
                </li>
            <?php endif; ?>

            <!-- lien pour caf -->
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('caf')): ?>
                <li class="nav-main-heading">CAF</li>
                <li class="nav-main-item<?php echo e(setMenuClass('caf.caisse.', ' open')); ?>">
                    <a class="nav-main-link nav-main-link-submenu" data-toggle="submenu" aria-haspopup="true"
                        aria-expanded="true" href="#">
                        <i class="nav-main-link-icon si si-wallet"></i>
                        <span class="nav-main-link-name">CAISSE</span>
                    </a>
                    <ul class="nav-main-submenu">
                        <li class="nav-main-item">
                            <a class="nav-main-link<?php echo e(setMenuClass('caf.caisse.inscription.index', ' active')); ?>"
                                href="<?php echo e(route('caf.caisse.inscription.index')); ?>">
                                <span class="nav-main-link-name">Inscription</span>
                            </a>
                        </li>
                        <li class="nav-main-item">
                            <a class="nav-main-link<?php echo e(setMenuClass('caf.caisse.payment.index', ' active')); ?>"
                                href="<?php echo e(route('caf.caisse.payment.index')); ?>">
                                <span class="nav-main-link-name">Payment pour les étudiants</span>
                            </a>
                        </li>
                        <li class="nav-main-item">
                            <a class="nav-main-link<?php echo e(setMenuClass('caf.caisse.todaypay.index', ' active')); ?>"
                                href="<?php echo e(route('caf.caisse.todaypay.index')); ?>">
                                <span class="nav-main-link-name">Payment du jour</span>
                            </a>
                        </li>
                        <li class="nav-main-item">
                            <a class="nav-main-link<?php echo e(setMenuClass('caf.caisse.histopay.index', ' active')); ?>"
                                href="<?php echo e(route('caf.caisse.histopay.index')); ?>">
                                <span class="nav-main-link-name">Historiques des paiments</span>
                            </a>
                        </li>
                        <li class="nav-main-item">
                            <a class="nav-main-link<?php echo e(setMenuClass('caf.caisse.infoetus.index', ' active')); ?>"
                                href="<?php echo e(route('caf.caisse.infoetus.index')); ?>">
                                <span class="nav-main-link-name">Information sur les étudiants</span>
                            </a>
                        </li>
                        <li class="nav-main-item">
                            <a class="nav-main-link<?php echo e(setMenuClass('caf.caisse.voiretats.index', ' active')); ?>"
                                href="<?php echo e(route('caf.caisse.voiretats.index')); ?>">
                                <span class="nav-main-link-name">Etat de paiments</span>
                            </a>
                        </li>
                        <li class="nav-main-item">
                            <a class="nav-main-link<?php echo e(setMenuClass('caf.caisse.diverspay.index', ' active')); ?>"
                                href="<?php echo e(route('caf.caisse.diverspay.index')); ?>">
                                <span class="nav-main-link-name">Paiement divers - Décaissement</span>
                            </a>
                        </li>
                        

                    </ul>
                </li>
            <?php endif; ?>
            <!-- lien pour secretaire -->
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('secretaire')): ?>
                <li class="nav-main-heading">secretaire</li>
                <li class="nav-main-item<?php echo e(setMenuClass('secretaire.gestion.', ' open')); ?>">
                    <a class="nav-main-link nav-main-link-submenu" data-toggle="submenu" aria-haspopup="true"
                        aria-expanded="true" href="#">
                        <i class="nav-main-link-icon si si-bulb"></i>
                        <span class="nav-main-link-name">Services Pedagogiques</span>
                    </a>
                    <ul class="nav-main-submenu">
                        <li class="nav-main-item">
                            <a class="nav-main-link<?php echo e(setMenuClass('secretaire.gestion.cours.index', ' active')); ?>"
                                href="<?php echo e(route('secretaire.gestion.cours.index')); ?>">
                                <span class="nav-main-link-name">Ajout Notes</span>
                            </a>
                        </li>
                        <li class="nav-main-item">
                            <a class="nav-main-link<?php echo e(setMenuClass('secretaire.gestion.inscription.index', ' active')); ?>"
                                href="<?php echo e(route('secretaire.gestion.inscription.index')); ?>">
                                <span class="nav-main-link-name">Validation Inscription</span>
                            </a>
                        </li>
                        <li class="nav-main-item">
                            <a class="nav-main-link<?php echo e(setMenuClass('secretaire.gestion.info.index', ' active')); ?>"
                                href="<?php echo e(route('secretaire.gestion.info.index')); ?>">
                                <span class="nav-main-link-name">Information sur les étudiants<</span>
                            </a>
                        </li>

                    </ul>
                </li>
            <?php endif; ?>

            <!-- lien pour etudiant -->
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('etudiant')): ?>
                <li class="nav-main-heading">etudiant</li>
                <li class="nav-main-item<?php echo e(request()->is('pages/*') ? ' open' : ''); ?>">
                    <a class="nav-main-link nav-main-link-submenu" data-toggle="submenu" aria-haspopup="true"
                        aria-expanded="true" href="#">
                        <i class="nav-main-link-icon si si-bulb"></i>
                        <span class="nav-main-link-name">Examples</span>
                    </a>
                    <ul class="nav-main-submenu">
                        <li class="nav-main-item">
                            <a class="nav-main-link<?php echo e(request()->is('pages/datatables') ? ' active' : ''); ?>"
                                href="/pages/datatables">
                                <span class="nav-main-link-name">DataTables</span>
                            </a>
                        </li>
                        <li class="nav-main-item">
                            <a class="nav-main-link<?php echo e(request()->is('pages/slick') ? ' active' : ''); ?>"
                                href="/pages/slick">
                                <span class="nav-main-link-name">Slick Slider</span>
                            </a>
                        </li>
                        <li class="nav-main-item">
                            <a class="nav-main-link<?php echo e(request()->is('pages/blank') ? ' active' : ''); ?>"
                                href="/pages/blank">
                                <span class="nav-main-link-name">Blank</span>
                            </a>
                        </li>
                    </ul>
                </li>
            <?php endif; ?>


        </ul>
    </div>
    <!-- END Side Navigation -->
</div>
<?php /**PATH C:\xampp\htdocs\ImsaaProject\resources\views/components/menu.blade.php ENDPATH**/ ?>