<!-- Hero avec statistiques -->
<div class="bg-body-light">
    <div class="content content-full">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <div>
                <h1 class="h3 fw-bold mb-1">Contacts des Étudiants</h1>
                <div class="d-flex gap-3 text-muted fs-sm">
                    <span><i class="fa fa-users me-1"></i> {{ $totalEtudiants }} étudiants</span>
                    @if ($query || $filtreParcours || $filtreNiveau || $filtreAnnee)
                        <span class="badge bg-info">Filtres actifs</span>
                    @endif
                </div>
            </div>
            <div class="d-flex gap-2">
                <button type="button" class="btn btn-sm btn-outline-primary" wire:click="toggleFilters">
                    <i class="fa fa-filter me-1"></i>
                    {{ $showFilters ? 'Masquer' : 'Afficher' }} filtres
                </button>
                <button type="button" class="btn btn-sm btn-outline-secondary" wire:click="toggleCompactView">
                    <i class="fa fa-{{ $compactView ? 'expand' : 'compress' }} me-1"></i>
                    Vue {{ $compactView ? 'détaillée' : 'compacte' }}
                </button>
                @if ($totalEtudiants > 0)
                    <button type="button" class="btn btn-sm btn-primary" wire:click="goToEtat">
                        <i class="fa fa-print me-1"></i> Générer PDF
                    </button>
                @endif
            </div>
        </div>

        <!-- Statistiques par parcours -->
        @if (count($totalParParcours) > 0)
            <div class="row g-2 mb-3">
                @foreach ($totalParParcours as $parcours => $count)
                    <div class="col-auto">
                        <span class="badge bg-secondary">{{ $parcours }}: {{ $count }}</span>
                    </div>
                @endforeach
            </div>
        @endif
    </div>
</div>
<!-- END Hero -->

<!-- Page Content -->
<div class="content">
    <!-- Filtres avancés -->
    @if ($showFilters)
        <div class="block block-rounded mb-3">
            <div class="block-header block-header-default">
                <h3 class="block-title">
                    <i class="fa fa-filter me-2"></i>Filtres de recherche
                </h3>
                <div class="block-options">
                    <button type="button" class="btn btn-sm btn-outline-danger" wire:click="clearAllFilters">
                        <i class="fa fa-times me-1"></i> Effacer tout
                    </button>
                </div>
            </div>
            <div class="block-content">
                <div class="row g-3">
                    <!-- Recherche textuelle -->
                    <div class="col-md-4">
                        <label class="form-label">Recherche</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fa fa-search"></i>
                            </span>
                            <input type="text" class="form-control" wire:model.debounce.300ms="query"
                                placeholder="Nom, prénom, matricule, téléphone...">
                            @if ($query)
                                <button class="btn btn-outline-secondary" type="button" wire:click="$set('query', '')">
                                    <i class="fa fa-times"></i>
                                </button>
                            @endif
                        </div>
                    </div>

                    <!-- Filtre Année -->
                    <div class="col-md-2">
                        <label class="form-label">Année Universitaire</label>
                        <select class="form-select" wire:model="filtreAnnee">
                            <option value="">Toutes les années</option>
                            @foreach ($annees as $annee)
                                <option value="{{ $annee->id }}">{{ $annee->nom }}</option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Filtre Parcours -->
                    <div class="col-md-3">
                        <label class="form-label">Parcours</label>
                        <select class="form-select" wire:model="filtreParcours">
                            <option value="">Tous les parcours</option>
                            @foreach ($parcourse as $parcour)
                                <option value="{{ $parcour->id }}">{{ $parcour->sigle }} - {{ $parcour->nom }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Filtre Niveau -->
                    <div class="col-md-2">
                        <label class="form-label">Niveau</label>
                        <select class="form-select" wire:model="filtreNiveau">
                            <option value="">Tous les niveaux</option>
                            @foreach ($niveaux as $niveau)
                                <option value="{{ $niveau->id }}">{{ $niveau->nom }}</option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Nombre par page -->
                    <div class="col-md-1">
                        <label class="form-label">Par page</label>
                        <select class="form-select" wire:model="perPage">
                            <option value="10">10</option>
                            <option value="15">15</option>
                            <option value="25">25</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- Liste des étudiants -->
    <div class="block block-rounded">
        <div class="block-header block-header-default">
            <h3 class="block-title">
                <i class="fa fa-address-book me-2"></i>Liste des contacts
                @if ($totalEtudiants > 0)
                    <span class="badge bg-primary ms-2">{{ $totalEtudiants }}</span>
                @endif
            </h3>
            <div class="block-options">
                @if ($isLoading)
                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                @endif
            </div>
        </div>

        <div class="block-content block-content-full">
            @if ($etudiants->count() > 0)
                <!-- Table responsive -->
                <div class="table-responsive">
                    <table
                        class="table table-bordered table-striped table-vcenter {{ $compactView ? 'table-sm' : '' }}">
                        <thead class="table-dark">
                            <tr>
                                @unless ($compactView)
                                    <th class="text-center" style="width: 50px;">#</th>
                                    <th class="text-center">
                                    <i class="fa fa-phone me-1"></i>Téléphone
                                </th>
                                @endunless
                                
                                <th wire:click="sortBy('nom')" class="cursor-pointer">
                                    <i class="fa fa-user me-1"></i>Nom
                                    @if ($sortField === 'nom')
                                        <i class="fa fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }} ms-1"></i>
                                    @endif
                                </th>
                                <th class="text-center">
                                    <i class="fa fa-phone me-1"></i>Prénom
                                </th>
                                @if($compactView)
                                <th class="text-center">
                                    <i class="fa fa-birthday-cake me-1"></i>Date de naissance
                                </th>
                                <th class="text-center">
                                    <i class="fa fa-map-marker-alt me-1"></i>Lieu de naissance
                                </th>
                                <th class="text-center">
                                    <i class="fa fa-home me-1"></i>Adresse
                                </th>
                                @endif
                                <th wire:click="sortBy('matricule')" class="cursor-pointer text-center">
                                    <i class="fa fa-id-card me-1"></i>Matricule
                                    @if ($sortField === 'matricule')
                                        <i class="fa fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }} ms-1"></i>
                                    @endif
                                </th>
                                @if($compactView)
                                <th class="text-center">
                                    <i class="fa fa-graduation-cap me-1"></i>Domaine
                                </th>
                                @endif
                                <th class="text-center">
                                    <i class="fa fa-graduation-cap me-1"></i>Parcours
                                </th>
                                <th class="text-center">
                                    <i class="fa fa-layer-group me-1"></i>Niveau
                                </th>
                                <th class="text-center">
                                    <i class="fa fa-calendar me-1"></i>Année
                                </th>
                                @unless ($compactView)
                                    <th class="text-center">
                                        <i class="fa fa-male me-1"></i>Nom du père
                                    </th>
                                    <th class="text-center">
                                        <i class="fa fa-female me-1"></i>Nom de la mère
                                    </th>
                                @endunless
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($etudiants as $etudiant)
                                @php
                                    $inscription = $etudiant->info->first();
                                @endphp
                                <tr>
                                    @unless ($compactView)
                                        <td class="text-center fw-bold">
                                            {{ $loop->iteration + ($etudiants->currentPage() - 1) * $etudiants->perPage() }}
                                        </td>
                                        <td class="text-center">
                                            <div class="fw-semibold text-primary">{{ $etudiant->telephone1 ?: 'N/A' }}</div>
                                            @if ($etudiant->telephone2)
                                                <small class="text-muted">{{ $etudiant->telephone2 }}</small>
                                            @endif
                                        </td>
                                    @endunless
                                    <td class="fw-semibold">
                                        <div class="d-flex align-items-center">
                                            @unless ($compactView)
                                                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2"
                                                    style="width: 32px; height: 32px; font-size: 14px;">
                                                    {{ substr($etudiant->nom, 0, 1) }}{{ substr($etudiant->prenom, 0, 1) }}
                                                </div>
                                            @endunless
                                            <div>
                                                <div>{{ $etudiant->nom }}</div>
                                                @unless ($compactView)
                                                    @if ($etudiant->sexe)
                                                        <small class="text-muted">
                                                            <i
                                                                class="fa fa-{{ $etudiant->sexe === 'M' ? 'male' : 'female' }} me-1"></i>
                                                            {{ $etudiant->sexe === 'M' ? 'Masculin' : 'Féminin' }}
                                                        </small>
                                                    @endif
                                                @endunless
                                            </div>
                                        </div>
                                    </td>
                                    <td class="">
                                        <div class="fw-semibold ">{{ $etudiant->prenom ?: ' ' }}</div>
                                    </td>
                                    @if($compactView)
                                    <td class="text-center text-muted">
                                        {{ $etudiant->date_naissance ? $etudiant->date_naissance : ' ' }}
                                    </td>
                                    <td class="text-center text-muted">{{ $etudiant->lieu_naissance ?: ' ' }}</td>
                                    <td class="text-center text-muted">{{ $etudiant->adresse ?: ' ' }}</td>
                                    @endif

                                    <td class="text-center">
                                        <span class="badge bg-secondary">{{ $etudiant->matricule ?: 'N/A' }}</span>
                                    </td>
                                    @if($compactView)
                                    <td class="text-center">
                                        @if ($inscription && $inscription->parcours)
                                            <span class="badge bg-info">{{ $inscription->parcours->mention->domaine->nom }}</span>
                                        @else
                                            <span class="badge bg-warning">Pas de domaine</span>
                                        @endif
                                    </td>
                                    @endif
                                    <td class="text-center">
                                        @if ($inscription && $inscription->parcours)
                                            <span class="badge bg-info">{{ $inscription->parcours->sigle }}</span>
                                            @unless ($compactView)
                                                <div class="small text-muted mt-1">{{ $inscription->parcours->nom }}</div>
                                            @endunless
                                        @else
                                            <span class="badge bg-warning">Pas de parcours</span>
                                        @endif
                                    </td>
                                    <td class="text-center">
                                        @if ($inscription && $inscription->niveau)
                                            <span class="badge bg-success">{{ $inscription->niveau->nom }}</span>
                                        @else
                                            <span class="badge bg-warning"> </span>
                                        @endif
                                    </td>
                                    <td class="text-center">
                                        @if ($inscription && $inscription->annee)
                                            <span class="badge bg-primary">{{ $inscription->annee->nom }}</span>
                                        @else
                                            <span class="badge bg-warning"> </span>
                                        @endif
                                    </td>
                                    @unless ($compactView)
                                        <td class="text-center text-muted">{{ $etudiant->nom_pere ?: ' ' }}</td>
                                        <td class="text-center text-muted">{{ $etudiant->nom_mere ?: ' ' }}</td>
                                    @endunless
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div class="text-muted">
                        Affichage de {{ $etudiants->firstItem() }} à {{ $etudiants->lastItem() }}
                        sur {{ $etudiants->total() }} résultats
                    </div>
                    <div>
                        {{ $etudiants->links() }}
                    </div>
                </div>
            @else
                <!-- État vide -->
                <div class="text-center py-5">
                    <div class="mb-3">
                        <i class="fa fa-users fa-3x text-muted"></i>
                    </div>
                    <h4 class="text-muted">Aucun étudiant trouvé</h4>
                    <p class="text-muted">
                        @if ($query || $filtreParcours || $filtreNiveau || $filtreAnnee)
                            Aucun étudiant ne correspond aux critères de recherche.
                            <br>
                            <button type="button" class="btn btn-outline-primary mt-2" wire:click="clearAllFilters">
                                <i class="fa fa-refresh me-1"></i> Effacer les filtres
                            </button>
                        @else
                            Aucun étudiant n'est enregistré dans le système.
                        @endif
                    </p>
                </div>
            @endif
        </div>
    </div>
</div>
<!-- END Page Content -->

<style>
    .cursor-pointer {
        cursor: pointer;
    }

    .cursor-pointer:hover {
        background-color: rgba(0, 0, 0, 0.05);
    }
</style>
