<?php

namespace App\Http\Livewire;

use App\Models\AnneeUniversitaire;
use App\Models\InscriptionStudent;
use App\Models\Niveau;
use App\Models\Parcour;
use App\Models\User;
use Carbon\Carbon;
use Livewire\Component;
use Livewire\WithPagination;

class ListeNewEtu extends Component
{
    use WithPagination;
    protected $paginationTheme = "bootstrap";

    public $currentPage = PAGELIST;

    // Propriétés pour les filtres avec valeurs par défaut
    public $filtreParcours = '';
    public $filtreNiveau = '';
    public $filtreAnnee = '';
    public $query = '';
    public $perPage = 15;

    // Propriétés pour l'affichage et l'UX
    public $isLoading = false;
    public $sortField = 'nom';
    public $sortDirection = 'asc';
    public $showFilters = true;
    public $compactView = false;

    // Statistiques
    public $totalEtudiants = 0;
    public $totalParParcours = [];

    // Propriétés héritées (maintenues pour compatibilité)
    public $newEtus = [];
    public $etus = [];
    public Parcour $current_parcours;
    public Niveau $current_niveau;
    public AnneeUniversitaire $current_annee;

    public function mount()
    {
        // Initialiser les filtres avec des valeurs par défaut intelligentes
        $this->filtreAnnee = AnneeUniversitaire::orderBy('nom', 'desc')->first()->id ?? '';
        $this->loadStatistics();
    }

    // Méthodes pour la réactivité des filtres
    public function updatingQuery() {
        $this->resetPage();
        $this->isLoading = true;
    }

    public function updatingFiltreAnnee() {
        $this->resetPage();
        $this->loadStatistics();
    }

    public function updatingFiltreParcours() {
        $this->resetPage();
        $this->loadStatistics();
    }

    public function updatingFiltreNiveau() {
        $this->resetPage();
        $this->loadStatistics();
    }

    public function updatingPerPage() {
        $this->resetPage();
    }

    // Méthodes pour améliorer l'UX
    public function sortBy($field)
    {
        if ($this->sortField === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortField = $field;
            $this->sortDirection = 'asc';
        }
        $this->resetPage();
    }

    public function toggleFilters()
    {
        $this->showFilters = !$this->showFilters;
    }

    public function toggleCompactView()
    {
        $this->compactView = !$this->compactView;
    }

    public function clearAllFilters()
    {
        $this->query = '';
        $this->filtreParcours = '';
        $this->filtreNiveau = '';
        $this->filtreAnnee = AnneeUniversitaire::orderBy('nom', 'desc')->first()->id ?? '';
        $this->resetPage();
        $this->loadStatistics();
    }

    public function loadStatistics()
    {
        // Calculer les statistiques basées sur les filtres actuels
        $baseQuery = User::whereHas('roles', fn($q) => $q->where('role_id', 5))
            ->whereHas('info', function($q) {
                $q->when($this->filtreAnnee, fn($query) => $query->where('annee_universitaire_id', $this->filtreAnnee))
                  ->when($this->filtreParcours, fn($query) => $query->where('parcour_id', $this->filtreParcours))
                  ->when($this->filtreNiveau, fn($query) => $query->where('niveau_id', $this->filtreNiveau));
            });

        $this->totalEtudiants = $baseQuery->count();

        // Statistiques par parcours
        $this->totalParParcours = User::whereHas('roles', fn($q) => $q->where('role_id', 5))
            ->whereHas('info', function($q) {
                $q->when($this->filtreAnnee, fn($query) => $query->where('annee_universitaire_id', $this->filtreAnnee))
                  ->when($this->filtreNiveau, fn($query) => $query->where('niveau_id', $this->filtreNiveau));
            })
            ->with(['info.parcours'])
            ->get()
            ->groupBy(function($user) {
                return $user->info->first()->parcours->sigle ?? 'Sans parcours';
            })
            ->map(fn($group) => $group->count())
            ->toArray();
    }

    public function render()
    {
        Carbon::setLocale("fr");

        // Construire la requête optimisée pour afficher tous les étudiants
        $etudiantsQuery = User::query()
            ->whereHas('roles', fn($q) => $q->where('role_id', 5))
            ->with([
                'info' => function($q) {
                    $q->with(['parcours.mention.domaine', 'niveau:id,nom', 'annee:id,nom'])
                      ->when($this->filtreAnnee, fn($query) => $query->where('annee_universitaire_id', $this->filtreAnnee))
                      ->when($this->filtreParcours, fn($query) => $query->where('parcour_id', $this->filtreParcours))
                      ->when($this->filtreNiveau, fn($query) => $query->where('niveau_id', $this->filtreNiveau));
                }
            ])
            ->whereHas('info', function($q) {
                $q->when($this->filtreAnnee, fn($query) => $query->where('annee_universitaire_id', $this->filtreAnnee))
                  ->when($this->filtreParcours, fn($query) => $query->where('parcour_id', $this->filtreParcours))
                  ->when($this->filtreNiveau, fn($query) => $query->where('niveau_id', $this->filtreNiveau));
            });

        // Appliquer la recherche textuelle
        if (!empty($this->query)) {
            $searchTerm = '%' . $this->query . '%';
            $etudiantsQuery->where(function($q) use ($searchTerm) {
                $q->where('nom', 'like', $searchTerm)
                  ->orWhere('prenom', 'like', $searchTerm)
                  ->orWhere('matricule', 'like', $searchTerm)
                  ->orWhere('telephone1', 'like', $searchTerm)
                  ->orWhereRaw("CONCAT(nom, ' ', prenom) LIKE ?", [$searchTerm]);
            });
        }

        // Appliquer le tri
        $etudiantsQuery->orderBy($this->sortField, $this->sortDirection);

        // Paginer les résultats
        $etudiants = $etudiantsQuery->paginate($this->perPage);

        return view('livewire.administration.contactEtu.index', [
            "etudiants" => $etudiants,
            "parcourse" => Parcour::all(),
            "niveaux" => Niveau::all(),
            "annees" => AnneeUniversitaire::orderBy('nom', 'desc')->get()
        ])
            ->extends('layouts.backend')
            ->section('content');
    }

    public function rules()
    {
        if ($this->currentPage == PAGEEDITFORM) {
            return [
                'editTypePayment.nom' => 'required',
            ];
        }

        return [
            'newEtus.niveau_id' => 'required',
            'newEtus.parcour_id' => 'required',
            'newEtus.annee_universitaire_id' => 'required',
        ];
    }

    public function goToListResult()
    {
        $this->currentPage = PAGELIST;
        $this->newEtus = [];
        $this->etus = [];
        $this->clearAllFilters();
    }

    // Méthode améliorée pour générer la liste avec les filtres actuels
    public function goToEtat()
    {
        // Utiliser les filtres actuels au lieu de la validation
        if (!empty($this->filtreAnnee) && !empty($this->filtreNiveau)) {
            $this->generateFromFilters();
            $this->currentPage = PAGECREATEFORM;
        } else {
            // Fallback vers l'ancienne méthode si nécessaire
            $validationAttributes = $this->validate();
            $this->generate(
                $validationAttributes["newEtus"]["parcour_id"],
                $validationAttributes["newEtus"]["niveau_id"],
                $validationAttributes["newEtus"]["annee_universitaire_id"]
            );
            $this->currentPage = PAGECREATEFORM;
        }
    }

    // Nouvelle méthode pour générer à partir des filtres actuels
    public function generateFromFilters()
    {
        $this->current_niveau = Niveau::find($this->filtreNiveau);
        $this->current_annee = AnneeUniversitaire::find($this->filtreAnnee);

        if (!empty($this->filtreParcours)) {
            $this->current_parcours = Parcour::find($this->filtreParcours);
        }

        // Utiliser la même logique que dans render() pour la cohérence
        $this->etus = User::whereHas('roles', fn($q) => $q->where('role_id', 5))
            ->whereHas('info', function($q) {
                $q->where('annee_universitaire_id', $this->filtreAnnee)
                  ->where('niveau_id', $this->filtreNiveau)
                  ->when($this->filtreParcours, fn($query) => $query->where('parcour_id', $this->filtreParcours));
            })
            ->when(!empty($this->query), function($q) {
                $searchTerm = '%' . $this->query . '%';
                $q->where(function($query) use ($searchTerm) {
                    $query->where('nom', 'like', $searchTerm)
                          ->orWhere('prenom', 'like', $searchTerm)
                          ->orWhere('matricule', 'like', $searchTerm)
                          ->orWhereRaw("CONCAT(nom, ' ', prenom) LIKE ?", [$searchTerm]);
                });
            })
            ->orderBy($this->sortField, $this->sortDirection)
            ->get();
    }

    // Méthode héritée maintenue pour compatibilité
    public function generate($parcour_id, $niveau_id, $annee_universitaire_id)
    {
        $this->current_niveau = Niveau::find($niveau_id);
        $this->current_annee = AnneeUniversitaire::find($annee_universitaire_id);

        if ($parcour_id == 100) {
            $this->etus = User::whereHas('info', fn ($q) => $q->whereAnneeUniversitaireId($annee_universitaire_id)->whereNiveauId($niveau_id))
                ->get();
        } else {
            $this->etus = User::whereHas('info', fn ($q) => $q->whereAnneeUniversitaireId($annee_universitaire_id)->whereNiveauId($niveau_id)->whereParcourId($parcour_id))
                ->get();
        }
    }
}
