{"__meta": {"id": "Xe266bc8e70be28fc01c891ffc81815e8", "datetime": "2025-07-24 12:22:06", "utime": **********.055414, "method": "GET", "uri": "/caisse/infoetus", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 3, "messages": [{"message": "[12:22:06] LOG.info: Detected N+1 Query", "message_html": null, "is_string": false, "label": "info", "time": **********.042181, "collector": "log"}, {"message": "[12:22:06] LOG.info: Model: App\\Models\\Parcour\r\nRelation: mention\r\nNum-Called: 9\r\nCall-Stack:\r\n#18 \\storage\\framework\\views\\d11fa9f7d96787013c79af3c8e17457d4f689f4a.php:271\r\n#20 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:84\r\n#21 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:59\r\n#22 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:195\r\n#23 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:178\r\n#24 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:147\r\n#25 \\storage\\framework\\views\\92b504728f85d515587be3849b313a81578bae63.php:18\r\n#27 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:84\r\n#28 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:59\r\n#29 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:195\r\n#30 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:178\r\n#31 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:147\r\n#32 \\vendor\\livewire\\livewire\\src\\Component.php:235\r\n#33 \\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\RenderView.php:14\r\n#34 \\vendor\\livewire\\livewire\\src\\LifecycleManager.php:141\r\n#35 \\storage\\framework\\views\\66b31d9ec95b294ed16d529d2a6853debf011f28.php:2\r\n#37 \\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php:110\r\n#38 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php:58\r\n#39 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:69\r\n#40 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php:70\r\n#41 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:35\r\n#42 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:195\r\n#43 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:178\r\n#44 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:147\r\n#45 \\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php:69\r\n#46 \\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php:35\r\n#47 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:906\r\n#48 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:875\r\n#49 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:797\r\n", "message_html": null, "is_string": false, "label": "info", "time": **********.042682, "collector": "log"}, {"message": "[12:22:06] LOG.info: Model: App\\Models\\Mention\r\nRelation: domaine\r\nNum-Called: 9\r\nCall-Stack:\r\n#18 \\storage\\framework\\views\\d11fa9f7d96787013c79af3c8e17457d4f689f4a.php:271\r\n#20 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:84\r\n#21 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:59\r\n#22 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:195\r\n#23 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:178\r\n#24 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:147\r\n#25 \\storage\\framework\\views\\92b504728f85d515587be3849b313a81578bae63.php:18\r\n#27 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:84\r\n#28 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:59\r\n#29 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:195\r\n#30 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:178\r\n#31 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:147\r\n#32 \\vendor\\livewire\\livewire\\src\\Component.php:235\r\n#33 \\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\RenderView.php:14\r\n#34 \\vendor\\livewire\\livewire\\src\\LifecycleManager.php:141\r\n#35 \\storage\\framework\\views\\66b31d9ec95b294ed16d529d2a6853debf011f28.php:2\r\n#37 \\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php:110\r\n#38 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php:58\r\n#39 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:69\r\n#40 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php:70\r\n#41 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:35\r\n#42 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:195\r\n#43 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:178\r\n#44 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:147\r\n#45 \\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php:69\r\n#46 \\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php:35\r\n#47 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:906\r\n#48 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:875\r\n#49 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:797\r\n", "message_html": null, "is_string": false, "label": "info", "time": **********.042986, "collector": "log"}]}, "time": {"start": **********.391355, "end": **********.05545, "duration": 3.***************, "duration_str": "3.66s", "measures": [{"label": "Booting", "start": **********.391355, "relative_start": 0, "end": **********.670428, "relative_end": **********.670428, "duration": 1.****************, "duration_str": "1.28s", "params": [], "collector": null}, {"label": "Application", "start": **********.671367, "relative_start": 1.****************, "end": **********.055453, "relative_end": 3.0994415283203125e-06, "duration": 2.****************, "duration_str": "2.38s", "params": [], "collector": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "27MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 7, "templates": [{"name": "C:\\xampp\\htdocs\\ImsaaProject\\vendor\\livewire\\livewire\\src/Macros/livewire-view-extends.blade.php (\\vendor\\livewire\\livewire\\src\\Macros\\livewire-view-extends.blade.php)", "param_count": 4, "params": ["view", "params", "slotOrSection", "manager"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\vendor\\livewire\\livewire\\src/Macros/livewire-view-extends.blade.php&line=0"}, {"name": "livewire.administration.contactEtu.index (\\resources\\views\\livewire\\administration\\contactEtu\\index.blade.php)", "param_count": 27, "params": ["etudiants", "parcourse", "niveaux", "annees", "livewireLayout", "errors", "_instance", "currentPage", "filtreParcours", "filtre<PERSON><PERSON>au", "filtreAnnee", "query", "perPage", "isLoading", "sortField", "sortDirection", "showFilters", "compactView", "totalEtudiants", "totalParParcours", "newEtus", "etus", "current_parcours", "current_niveau", "current_annee", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/administration/contactEtu/index.blade.php&line=0"}, {"name": "livewire.administration.contactEtu.liste (\\resources\\views\\livewire\\administration\\contactEtu\\liste.blade.php)", "param_count": 29, "params": ["__env", "app", "errors", "_instance", "etudiants", "parcourse", "niveaux", "annees", "livewireLayout", "currentPage", "filtreParcours", "filtre<PERSON><PERSON>au", "filtreAnnee", "query", "perPage", "isLoading", "sortField", "sortDirection", "showFilters", "compactView", "totalEtudiants", "totalParParcours", "newEtus", "etus", "current_parcours", "current_niveau", "current_annee", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/administration/contactEtu/liste.blade.php&line=0"}, {"name": "livewire::bootstrap (\\vendor\\livewire\\livewire\\src\\views\\pagination\\bootstrap.blade.php)", "param_count": 2, "params": ["paginator", "elements"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\vendor\\livewire\\livewire\\src\\views\\pagination/bootstrap.blade.php&line=0"}, {"name": "layouts.backend (\\resources\\views\\layouts\\backend.blade.php)", "param_count": 7, "params": ["__env", "app", "errors", "view", "params", "slotOrSection", "manager"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/layouts/backend.blade.php&line=0"}, {"name": "components.menu (\\resources\\views\\components\\menu.blade.php)", "param_count": 3, "params": ["attributes", "slot", "__laravel_slots"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/components/menu.blade.php&line=0"}, {"name": "components.rightHeader (\\resources\\views\\components\\rightHeader.blade.php)", "param_count": 3, "params": ["attributes", "slot", "__laravel_slots"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/components/rightHeader.blade.php&line=0"}]}, "route": {"uri": "GET caisse/infoetus", "middleware": "web, auth, auth.caf", "controller": "App\\Http\\Livewire\\ListeNewEtu@__invoke", "as": "caf.caisse.infoetus.index", "namespace": null, "prefix": "/caisse", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\vendor\\livewire\\livewire\\src\\Component.php&line=46\">\\vendor\\livewire\\livewire\\src\\Component.php:46-88</a>"}, "queries": {"nb_statements": 41, "nb_failed_statements": 0, "accumulated_duration": 0.5342300000000002, "accumulated_duration_str": "534ms", "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.01052, "duration_str": "10.52ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:59", "connection": "imsaaapp", "start_percent": 0, "width_percent": 1.969}, {"sql": "select `roles`.*, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 1 and `nom` = 'superadmin' limit 1", "type": "query", "params": [], "bindings": ["1", "superadmin"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Models\\User.php", "line": 55}, {"index": 16, "namespace": null, "name": "\\app\\Providers\\AuthServiceProvider.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 553}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 425}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 395}], "duration": 0.00608, "duration_str": "6.08ms", "stmt_id": "\\app\\Models\\User.php:55", "connection": "imsaaapp", "start_percent": 1.969, "width_percent": 1.138}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`deleted_at` is null order by `nom` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 49}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.03516, "duration_str": "35.16ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:49", "connection": "imsaaapp", "start_percent": 3.107, "width_percent": 6.581}, {"sql": "select count(*) as aggregate from `users` where exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `role_id` = 5) and exists (select * from `inscription_students` where `users`.`id` = `inscription_students`.`user_id` and `annee_universitaire_id` = 6 and `inscription_students`.`deleted_at` is null) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "6"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 120}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 50}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00474, "duration_str": "4.74ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:120", "connection": "imsaaapp", "start_percent": 9.689, "width_percent": 0.887}, {"sql": "select * from `users` where exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `role_id` = 5) and exists (select * from `inscription_students` where `users`.`id` = `inscription_students`.`user_id` and `annee_universitaire_id` = 6 and `inscription_students`.`deleted_at` is null) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "6"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 129}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 50}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.010119999999999999, "duration_str": "10.12ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:129", "connection": "imsaaapp", "start_percent": 10.576, "width_percent": 1.894}, {"sql": "select * from `inscription_students` where `inscription_students`.`user_id` in (4, 9, 12, 13, 14, 16, 17, 27, 28, 29, 39, 40, 41, 42, 43, 45, 46, 47, 48, 52, 53, 56, 57, 59, 66, 69, 71, 72, 73, 75, 77, 80, 85, 88, 91, 92, 93, 94, 95, 96, 98, 99, 100, 103, 104, 106, 107, 109, 110, 111, 112, 113, 114, 120, 130, 131, 132, 133, 139, 144, 185, 293, 295, 299, 300, 301, 303, 304, 305, 306, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 340, 342, 344, 345, 346, 347, 351, 352, 354, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 367, 368, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 383, 385, 387, 388, 389, 390, 392, 394, 396, 397, 398, 399, 400, 401, 402, 403, 404, 406, 407, 408, 409, 410, 411, 412, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426, 437, 438, 439, 441, 442, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 461, 462, 463, 465, 466, 467, 468, 469, 470, 471, 472, 473, 474, 475, 476, 477, 478, 479, 480, 481, 482, 483, 484, 485, 486, 487, 488, 489, 490, 491, 492, 493, 494, 495, 496, 497, 498, 499, 503, 504, 505, 506, 507, 508, 509) and `inscription_students`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 129}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 50}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.01458, "duration_str": "14.58ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:129", "connection": "imsaaapp", "start_percent": 12.47, "width_percent": 2.729}, {"sql": "select * from `parcours` where `parcours`.`id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 15, 17, 19, 21, 22, 24) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 129}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 50}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.02032, "duration_str": "20.32ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:129", "connection": "imsaaapp", "start_percent": 15.199, "width_percent": 3.804}, {"sql": "select count(*) as aggregate from `users` where exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `role_id` = 5) and exists (select * from `inscription_students` where `users`.`id` = `inscription_students`.`user_id` and `annee_universitaire_id` = 6 and `inscription_students`.`deleted_at` is null) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "6"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 174}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.05257, "duration_str": "52.57ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:174", "connection": "imsaaapp", "start_percent": 19.003, "width_percent": 9.84}, {"sql": "select * from `users` where exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `role_id` = 5) and exists (select * from `inscription_students` where `users`.`id` = `inscription_students`.`user_id` and `annee_universitaire_id` = 6 and `inscription_students`.`deleted_at` is null) and `users`.`deleted_at` is null order by `nom` asc limit 15 offset 0", "type": "query", "params": [], "bindings": ["5", "6"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 174}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.04393, "duration_str": "43.93ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:174", "connection": "imsaaapp", "start_percent": 28.843, "width_percent": 8.223}, {"sql": "select * from `inscription_students` where `inscription_students`.`user_id` in (12, 16, 103, 114, 185, 327, 334, 396, 404, 410, 449, 465, 479, 492, 494) and `annee_universitaire_id` = 6 and `inscription_students`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 174}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.039560000000000005, "duration_str": "39.56ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:174", "connection": "imsaaapp", "start_percent": 37.066, "width_percent": 7.405}, {"sql": "select `id`, `sigle`, `nom`, `mention_id` from `parcours` where `parcours`.`id` in (1, 5, 8, 10, 12, 14, 15, 19, 22) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 174}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.01833, "duration_str": "18.33ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:174", "connection": "imsaaapp", "start_percent": 44.471, "width_percent": 3.431}, {"sql": "select `id`, `nom` from `niveaux` where `niveaux`.`id` in (1, 2, 3, 4) and `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 174}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.01132, "duration_str": "11.32ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:174", "connection": "imsaaapp", "start_percent": 47.903, "width_percent": 2.119}, {"sql": "select `id`, `nom` from `annee_universitaires` where `annee_universitaires`.`id` in (6) and `annee_universitaires`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 174}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00758, "duration_str": "7.58ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:174", "connection": "imsaaapp", "start_percent": 50.022, "width_percent": 1.419}, {"sql": "select * from `parcours` where `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 178}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.012289999999999999, "duration_str": "12.29ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:178", "connection": "imsaaapp", "start_percent": 51.44, "width_percent": 2.301}, {"sql": "select * from `niveaux` where `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 179}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.01031, "duration_str": "10.31ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:179", "connection": "imsaaapp", "start_percent": 53.741, "width_percent": 1.93}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`deleted_at` is null order by `nom` desc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 180}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0025800000000000003, "duration_str": "2.58ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:180", "connection": "imsaaapp", "start_percent": 55.671, "width_percent": 0.483}, {"sql": "select * from `mentions` where `mentions`.`id` = 9 and `mentions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["9"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "d11fa9f7d96787013c79af3c8e17457d4f689f4a", "line": 271}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.16941, "duration_str": "169ms", "stmt_id": "view::d11fa9f7d96787013c79af3c8e17457d4f689f4a:271", "connection": "imsaaapp", "start_percent": 56.154, "width_percent": 31.711}, {"sql": "select * from `domaines` where `domaines`.`id` = 1 and `domaines`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "d11fa9f7d96787013c79af3c8e17457d4f689f4a", "line": 271}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.01199, "duration_str": "11.99ms", "stmt_id": "view::d11fa9f7d96787013c79af3c8e17457d4f689f4a:271", "connection": "imsaaapp", "start_percent": 87.865, "width_percent": 2.244}, {"sql": "select * from `mentions` where `mentions`.`id` = 8 and `mentions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "d11fa9f7d96787013c79af3c8e17457d4f689f4a", "line": 271}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00359, "duration_str": "3.59ms", "stmt_id": "view::d11fa9f7d96787013c79af3c8e17457d4f689f4a:271", "connection": "imsaaapp", "start_percent": 90.109, "width_percent": 0.672}, {"sql": "select * from `domaines` where `domaines`.`id` = 2 and `domaines`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "d11fa9f7d96787013c79af3c8e17457d4f689f4a", "line": 271}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.0025499999999999997, "duration_str": "2.55ms", "stmt_id": "view::d11fa9f7d96787013c79af3c8e17457d4f689f4a:271", "connection": "imsaaapp", "start_percent": 90.781, "width_percent": 0.477}, {"sql": "select * from `mentions` where `mentions`.`id` = 2 and `mentions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "d11fa9f7d96787013c79af3c8e17457d4f689f4a", "line": 271}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00076, "duration_str": "760μs", "stmt_id": "view::d11fa9f7d96787013c79af3c8e17457d4f689f4a:271", "connection": "imsaaapp", "start_percent": 91.258, "width_percent": 0.142}, {"sql": "select * from `domaines` where `domaines`.`id` = 1 and `domaines`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "d11fa9f7d96787013c79af3c8e17457d4f689f4a", "line": 271}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00277, "duration_str": "2.77ms", "stmt_id": "view::d11fa9f7d96787013c79af3c8e17457d4f689f4a:271", "connection": "imsaaapp", "start_percent": 91.401, "width_percent": 0.519}, {"sql": "select * from `mentions` where `mentions`.`id` = 4 and `mentions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "d11fa9f7d96787013c79af3c8e17457d4f689f4a", "line": 271}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00484, "duration_str": "4.84ms", "stmt_id": "view::d11fa9f7d96787013c79af3c8e17457d4f689f4a:271", "connection": "imsaaapp", "start_percent": 91.919, "width_percent": 0.906}, {"sql": "select * from `domaines` where `domaines`.`id` = 1 and `domaines`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "d11fa9f7d96787013c79af3c8e17457d4f689f4a", "line": 271}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00679, "duration_str": "6.79ms", "stmt_id": "view::d11fa9f7d96787013c79af3c8e17457d4f689f4a:271", "connection": "imsaaapp", "start_percent": 92.825, "width_percent": 1.271}, {"sql": "select * from `mentions` where `mentions`.`id` = 4 and `mentions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "d11fa9f7d96787013c79af3c8e17457d4f689f4a", "line": 271}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00175, "duration_str": "1.75ms", "stmt_id": "view::d11fa9f7d96787013c79af3c8e17457d4f689f4a:271", "connection": "imsaaapp", "start_percent": 94.096, "width_percent": 0.328}, {"sql": "select * from `domaines` where `domaines`.`id` = 1 and `domaines`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "d11fa9f7d96787013c79af3c8e17457d4f689f4a", "line": 271}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00063, "duration_str": "630μs", "stmt_id": "view::d11fa9f7d96787013c79af3c8e17457d4f689f4a:271", "connection": "imsaaapp", "start_percent": 94.424, "width_percent": 0.118}, {"sql": "select * from `mentions` where `mentions`.`id` = 7 and `mentions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "d11fa9f7d96787013c79af3c8e17457d4f689f4a", "line": 271}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.0014199999999999998, "duration_str": "1.42ms", "stmt_id": "view::d11fa9f7d96787013c79af3c8e17457d4f689f4a:271", "connection": "imsaaapp", "start_percent": 94.542, "width_percent": 0.266}, {"sql": "select * from `domaines` where `domaines`.`id` = 2 and `domaines`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "d11fa9f7d96787013c79af3c8e17457d4f689f4a", "line": 271}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.0051600000000000005, "duration_str": "5.16ms", "stmt_id": "view::d11fa9f7d96787013c79af3c8e17457d4f689f4a:271", "connection": "imsaaapp", "start_percent": 94.807, "width_percent": 0.966}, {"sql": "select * from `mentions` where `mentions`.`id` = 6 and `mentions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "d11fa9f7d96787013c79af3c8e17457d4f689f4a", "line": 271}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00136, "duration_str": "1.36ms", "stmt_id": "view::d11fa9f7d96787013c79af3c8e17457d4f689f4a:271", "connection": "imsaaapp", "start_percent": 95.773, "width_percent": 0.255}, {"sql": "select * from `domaines` where `domaines`.`id` = 2 and `domaines`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "d11fa9f7d96787013c79af3c8e17457d4f689f4a", "line": 271}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00108, "duration_str": "1.08ms", "stmt_id": "view::d11fa9f7d96787013c79af3c8e17457d4f689f4a:271", "connection": "imsaaapp", "start_percent": 96.028, "width_percent": 0.202}, {"sql": "select * from `mentions` where `mentions`.`id` = 2 and `mentions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "d11fa9f7d96787013c79af3c8e17457d4f689f4a", "line": 271}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00646, "duration_str": "6.46ms", "stmt_id": "view::d11fa9f7d96787013c79af3c8e17457d4f689f4a:271", "connection": "imsaaapp", "start_percent": 96.23, "width_percent": 1.209}, {"sql": "select * from `domaines` where `domaines`.`id` = 1 and `domaines`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "d11fa9f7d96787013c79af3c8e17457d4f689f4a", "line": 271}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00079, "duration_str": "790μs", "stmt_id": "view::d11fa9f7d96787013c79af3c8e17457d4f689f4a:271", "connection": "imsaaapp", "start_percent": 97.439, "width_percent": 0.148}, {"sql": "select * from `mentions` where `mentions`.`id` = 5 and `mentions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "d11fa9f7d96787013c79af3c8e17457d4f689f4a", "line": 271}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.0009699999999999999, "duration_str": "970μs", "stmt_id": "view::d11fa9f7d96787013c79af3c8e17457d4f689f4a:271", "connection": "imsaaapp", "start_percent": 97.587, "width_percent": 0.182}, {"sql": "select * from `domaines` where `domaines`.`id` = 1 and `domaines`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "d11fa9f7d96787013c79af3c8e17457d4f689f4a", "line": 271}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 178}], "duration": 0.00095, "duration_str": "950μs", "stmt_id": "view::d11fa9f7d96787013c79af3c8e17457d4f689f4a:271", "connection": "imsaaapp", "start_percent": 97.769, "width_percent": 0.178}, {"sql": "select `roles`.*, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 1 and `nom` = 'superadmin' limit 1", "type": "query", "params": [], "bindings": ["1", "superadmin"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Models\\User.php", "line": 55}, {"index": 16, "namespace": null, "name": "\\app\\Providers\\AuthServiceProvider.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 553}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 425}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 395}], "duration": 0.00094, "duration_str": "940μs", "stmt_id": "\\app\\Models\\User.php:55", "connection": "imsaaapp", "start_percent": 97.947, "width_percent": 0.176}, {"sql": "select `roles`.*, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 1 and `nom` = 'superadmin' limit 1", "type": "query", "params": [], "bindings": ["1", "superadmin"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Models\\User.php", "line": 55}, {"index": 16, "namespace": null, "name": "\\app\\Providers\\AuthServiceProvider.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 553}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 425}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 395}], "duration": 0.0014399999999999999, "duration_str": "1.44ms", "stmt_id": "\\app\\Models\\User.php:55", "connection": "imsaaapp", "start_percent": 98.123, "width_percent": 0.27}, {"sql": "select `roles`.*, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 1 and `nom` = 'superadmin' limit 1", "type": "query", "params": [], "bindings": ["1", "superadmin"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Models\\User.php", "line": 55}, {"index": 16, "namespace": null, "name": "\\app\\Providers\\AuthServiceProvider.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 553}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 425}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 395}], "duration": 0.0010500000000000002, "duration_str": "1.05ms", "stmt_id": "\\app\\Models\\User.php:55", "connection": "imsaaapp", "start_percent": 98.392, "width_percent": 0.197}, {"sql": "select `roles`.*, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 1 and `nom` = 'superadmin' limit 1", "type": "query", "params": [], "bindings": ["1", "superadmin"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Models\\User.php", "line": 55}, {"index": 16, "namespace": null, "name": "\\app\\Providers\\AuthServiceProvider.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 553}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 425}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 395}], "duration": 0.00094, "duration_str": "940μs", "stmt_id": "\\app\\Models\\User.php:55", "connection": "imsaaapp", "start_percent": 98.589, "width_percent": 0.176}, {"sql": "select `roles`.*, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 1 and `nom` = 'superadmin' limit 1", "type": "query", "params": [], "bindings": ["1", "superadmin"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Models\\User.php", "line": 55}, {"index": 16, "namespace": null, "name": "\\app\\Providers\\AuthServiceProvider.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 553}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 425}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 395}], "duration": 0.00214, "duration_str": "2.14ms", "stmt_id": "\\app\\Models\\User.php:55", "connection": "imsaaapp", "start_percent": 98.765, "width_percent": 0.401}, {"sql": "select `roles`.*, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 1 and `nom` = 'superadmin' limit 1", "type": "query", "params": [], "bindings": ["1", "superadmin"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Models\\User.php", "line": 55}, {"index": 16, "namespace": null, "name": "\\app\\Providers\\AuthServiceProvider.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 553}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 425}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 395}], "duration": 0.0029500000000000004, "duration_str": "2.95ms", "stmt_id": "\\app\\Models\\User.php:55", "connection": "imsaaapp", "start_percent": 99.165, "width_percent": 0.552}, {"sql": "select `roles`.*, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Helpers\\helpers.php", "line": 18}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "duration": 0.00151, "duration_str": "1.51ms", "stmt_id": "\\app\\Helpers\\helpers.php:18", "connection": "imsaaapp", "start_percent": 99.717, "width_percent": 0.283}]}, "models": {"data": {"App\\Models\\Domaine": 9, "App\\Models\\Mention": 9, "App\\Models\\Niveau": 9, "App\\Models\\Parcour": 52, "App\\Models\\InscriptionStudent": 476, "App\\Models\\AnneeUniversitaire": 8, "App\\Models\\Role": 8, "App\\Models\\User": 248}, "count": 819}, "livewire": {"data": {"liste-new-etu #gBKzB83520wG24cxWVmn": "array:5 [\n  \"data\" => array:20 [\n    \"currentPage\" => \"liste\"\n    \"filtreParcours\" => \"\"\n    \"filtreNiveau\" => \"\"\n    \"filtreAnnee\" => 6\n    \"query\" => \"\"\n    \"perPage\" => 15\n    \"isLoading\" => false\n    \"sortField\" => \"nom\"\n    \"sortDirection\" => \"asc\"\n    \"showFilters\" => true\n    \"compactView\" => false\n    \"totalEtudiants\" => 232\n    \"totalParParcours\" => array:14 [\n      \"MAT\" => 2\n      \"THR\" => 37\n      \"CM\" => 18\n      \"GFC\" => 17\n      \"TBA\" => 11\n      \"TD\" => 64\n      \"GBC\" => 11\n      \"GPM\" => 5\n      \"GEE\" => 18\n      \"GRT\" => 13\n      \"LCI\" => 24\n      \"ISE\" => 5\n      \"ADA\" => 6\n      \"ICA\" => 1\n    ]\n    \"newEtus\" => []\n    \"etus\" => []\n    \"current_parcours\" => null\n    \"current_niveau\" => null\n    \"current_annee\" => null\n    \"page\" => 1\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"liste-new-etu\"\n  \"view\" => \"livewire.administration.contactEtu.index\"\n  \"component\" => \"App\\Http\\Livewire\\ListeNewEtu\"\n  \"id\" => \"gBKzB83520wG24cxWVmn\"\n]"}, "count": 1}, "gate": {"count": 7, "messages": [{"message": "[ability => caf, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-734120834 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"3 characters\">caf</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-734120834\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753348924.004896}, {"message": "[ability => admin, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-413001769 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">admin</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-413001769\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753348925.865495}, {"message": "[ability => enseignant, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-904174267 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">enseignant</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-904174267\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753348925.874025}, {"message": "[ability => deraq, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-674847765 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">deraq</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-674847765\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753348925.88192}, {"message": "[ability => caf, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-903900016 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"3 characters\">caf</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-903900016\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753348925.888788}, {"message": "[ability => secretaire, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1973125041 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">secretaire</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1973125041\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753348925.897659}, {"message": "[ability => etudiant, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1539139973 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">etudiant</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1539139973\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753348925.906177}]}, "session": {"_token": "VtnJlwxOpd0hRXXY8oKOSLlbl0TFeAO2LM004ptd", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/caisse/infoetus\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1753344491\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/caisse/infoetus", "status_code": "<pre class=sf-dump id=sf-dump-907020295 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-907020295\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-2099396636 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2099396636\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1104730865 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1104730865\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-163724208 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ik9pQmZBUXIvSW90YkxtbXN3ckZxUWc9PSIsInZhbHVlIjoiS0EyL2xTQW9ZL1J2ajlzZG44Sld0dnhNUHJDRUxvejl2S2d1Tm5QekN2Y0JpTlZIZHd5ajJTeHNadnViM09rL2NjVCs2cDE2MnlXaGw1TW4vaXFuNUlpTDlrUktLbUtFV3M2MitJMC8zbmQ3aWZ5cWJNejUzaDdDOTg4akw2aEMiLCJtYWMiOiJlYjkxNzk0ZTdmNjNkNjhkYmMxY2M4ZWFmZDBmZGE5OGVmNjE2OTk2NTU5YzQ2NGMyOWQ1MzhhMmYzNWYzYjMzIiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6InJ1eEJNM1M1RzZocUpzVDN5bnpIc2c9PSIsInZhbHVlIjoiT1lOMC83WWNpbStsN201RnJTejJvL3hON3IySFdGQkpGaU53TlBqZ2xFM0tQUU1xM3JKYW1xa3dhZ1EwVnBFYWhIM1B6T1pyWWN3ZjVTWTlqZjgybVBUSnUxTzRmZVBHcUo1Yjg5QmQwSE5TalNnWitWcWh4SEJsU1YweURtbkgiLCJtYWMiOiI4ZWMzYjRmZTg3OWI2OWEzOGFjYzBkZDA5MTE0YjY0YTEyNjc4ZDc1OWM2MWFiNzlkOTc3MDM5NTA4ZmE0MGIzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-163724208\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-340287173 data-indent-pad=\"  \"><span class=sf-dump-note>array:32</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"35 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">57416</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"16 characters\">/caisse/infoetus</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"45 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"16 characters\">/caisse/infoetus</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"26 characters\">/index.php/caisse/infoetus</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ik9pQmZBUXIvSW90YkxtbXN3ckZxUWc9PSIsInZhbHVlIjoiS0EyL2xTQW9ZL1J2ajlzZG44Sld0dnhNUHJDRUxvejl2S2d1Tm5QekN2Y0JpTlZIZHd5ajJTeHNadnViM09rL2NjVCs2cDE2MnlXaGw1TW4vaXFuNUlpTDlrUktLbUtFV3M2MitJMC8zbmQ3aWZ5cWJNejUzaDdDOTg4akw2aEMiLCJtYWMiOiJlYjkxNzk0ZTdmNjNkNjhkYmMxY2M4ZWFmZDBmZGE5OGVmNjE2OTk2NTU5YzQ2NGMyOWQ1MzhhMmYzNWYzYjMzIiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6InJ1eEJNM1M1RzZocUpzVDN5bnpIc2c9PSIsInZhbHVlIjoiT1lOMC83WWNpbStsN201RnJTejJvL3hON3IySFdGQkpGaU53TlBqZ2xFM0tQUU1xM3JKYW1xa3dhZ1EwVnBFYWhIM1B6T1pyWWN3ZjVTWTlqZjgybVBUSnUxTzRmZVBHcUo1Yjg5QmQwSE5TalNnWitWcWh4SEJsU1YweURtbkgiLCJtYWMiOiI4ZWMzYjRmZTg3OWI2OWEzOGFjYzBkZDA5MTE0YjY0YTEyNjc4ZDc1OWM2MWFiNzlkOTc3MDM5NTA4ZmE0MGIzIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>**********.3914</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>**********</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-340287173\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-678293346 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VtnJlwxOpd0hRXXY8oKOSLlbl0TFeAO2LM004ptd</span>\"\n  \"<span class=sf-dump-key>scolarite_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RKkczZOcD1TSWkk2eKq2gL5rBSt8JPLoI0Me9VkZ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-678293346\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-358864865 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 24 Jul 2025 09:22:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IldNcFlJRlNNanhCMmIzazhXS0VuT2c9PSIsInZhbHVlIjoicEtRemRsci9ERFNvTTcvRWwrbXJ2WmRBVzUybXZFc2hYN0tHM0E1MTl6dnYyamlwa2dRQlJLZ2ZyRU9vUWVqaWNIeUQ3V1gvMWpTVStnbGdNeGkwY3QzTGJxTi9LMUp5MVlGeWs1SjZYN0hHc1ZiZ3JEbWY3YmlVcnphN0lJRUMiLCJtYWMiOiI0ZWM0Njc4MmUzMDA1NTc4Mjk1YzM2ZTE4YmI4NWU3YzYwN2MyZjA0NjAwYzkxYTExM2E0Y2M2NGJlNDhjNzNiIiwidGFnIjoiIn0%3D; expires=Thu, 24 Jul 2025 11:22:05 GMT; Max-Age=7199; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"451 characters\">scolarite_imsaa_session=eyJpdiI6InNVSnBwdG1WaTNKbExkdDQzT3h6ZkE9PSIsInZhbHVlIjoiWUhRaVd3ZXNEZllvZ3FJWEZYcFZMZVRFazIzWVE4b3d1ZnNKV3JLbTB3QVlodzlTNHBlb2dpdnhzTXhVeTgvaW9KbFN2NmVhMVN4cS9DWldsak1pK0prVXhKekpvSnRHYUJoMEdzcGNic0phLzdlNit2K1ZEbE5BQTRJNHloTzMiLCJtYWMiOiJhZjg2N2NkM2I4Mjg3YjUyMjkwODVjNDdkMDdiNTQ0MmNkMTk4YWM5NzVkYzliNmY0M2M0YTZmY2JhYTAwM2Y1IiwidGFnIjoiIn0%3D; expires=Thu, 24 Jul 2025 11:22:05 GMT; Max-Age=7199; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IldNcFlJRlNNanhCMmIzazhXS0VuT2c9PSIsInZhbHVlIjoicEtRemRsci9ERFNvTTcvRWwrbXJ2WmRBVzUybXZFc2hYN0tHM0E1MTl6dnYyamlwa2dRQlJLZ2ZyRU9vUWVqaWNIeUQ3V1gvMWpTVStnbGdNeGkwY3QzTGJxTi9LMUp5MVlGeWs1SjZYN0hHc1ZiZ3JEbWY3YmlVcnphN0lJRUMiLCJtYWMiOiI0ZWM0Njc4MmUzMDA1NTc4Mjk1YzM2ZTE4YmI4NWU3YzYwN2MyZjA0NjAwYzkxYTExM2E0Y2M2NGJlNDhjNzNiIiwidGFnIjoiIn0%3D; expires=Thu, 24-Jul-2025 11:22:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"423 characters\">scolarite_imsaa_session=eyJpdiI6InNVSnBwdG1WaTNKbExkdDQzT3h6ZkE9PSIsInZhbHVlIjoiWUhRaVd3ZXNEZllvZ3FJWEZYcFZMZVRFazIzWVE4b3d1ZnNKV3JLbTB3QVlodzlTNHBlb2dpdnhzTXhVeTgvaW9KbFN2NmVhMVN4cS9DWldsak1pK0prVXhKekpvSnRHYUJoMEdzcGNic0phLzdlNit2K1ZEbE5BQTRJNHloTzMiLCJtYWMiOiJhZjg2N2NkM2I4Mjg3YjUyMjkwODVjNDdkMDdiNTQ0MmNkMTk4YWM5NzVkYzliNmY0M2M0YTZmY2JhYTAwM2Y1IiwidGFnIjoiIn0%3D; expires=Thu, 24-Jul-2025 11:22:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-358864865\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-448984681 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VtnJlwxOpd0hRXXY8oKOSLlbl0TFeAO2LM004ptd</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/caisse/infoetus</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1753344491</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-448984681\", {\"maxDepth\":0})</script>\n"}}