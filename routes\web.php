<?php

use App\Http\Controllers\PdfController;
use App\Http\Livewire\AjoutNotes;
use App\Http\Livewire\AnneeUniversitaires;
use App\Http\Livewire\CafPay;
use App\Http\Livewire\CertificatScolarite;
use App\Http\Livewire\Cours;
use App\Http\Livewire\CoursEnseignant;
use App\Http\Livewire\CoursSecretaire;
use App\Http\Livewire\DebtEtu;
use App\Http\Livewire\DiversPay;
use App\Http\Livewire\Enseignant;
use App\Http\Livewire\Utilisateurs;
use App\Http\Livewire\Parcours;
use App\Http\Livewire\Domaines;
use App\Http\Livewire\Etudiant;
use App\Http\Livewire\EtuPay;
use App\Http\Livewire\FigerNote;
use App\Http\Livewire\HistoPay;
use App\Http\Livewire\TodayPayment;
use App\Http\Livewire\HistoriqueNotes;
use App\Http\Livewire\PaymentEtu;
use App\Http\Livewire\TypePay;
use App\Http\Livewire\InfoEtu;
use App\Http\Livewire\InscriptionField;
use App\Http\Livewire\ListeNewEtu;
use App\Http\Livewire\Mentions;
use App\Http\Livewire\NoteEtu;
use App\Http\Livewire\NoteStatus;
use App\Http\Livewire\Notes;
use App\Http\Livewire\PayStatus;
use App\Http\Livewire\PromtionEtu;
use App\Http\Livewire\Releve;
use App\Http\Livewire\Result;
use App\Http\Livewire\Semestres;
use App\Http\Livewire\Ues;
use App\Http\Livewire\Rattrapage;
use App\Http\Livewire\ViewNote;
use App\Http\Livewire\VoirEtat;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Auth::routes();

Route::get('/', [App\Http\Controllers\HomeController::class, 'index'])->name('home');
Route::get('/resetpass', [App\Http\Controllers\HomeController::class, 'resetPass'])->name('resetpass');

Route::get('/change-password', [App\Http\Controllers\HomeController::class, 'changePassword'])->name('change-password');

Route::post('/change-password', [App\Http\Controllers\HomeController::class, 'updatePassword'])->name('update-password');

Route::get('/data', function () {
    return view('pdf.resultat');
});


Route::group([
    "middleware" => ["auth", "auth.deraq"],
    'as' => 'deraq.'
], function () {

    Route::group([
        "prefix" => "gestions",
        'as' => 'gestions.'
    ], function () {
        Route::get("/enseignants", Enseignant::class)->name("enseignants.index");
        Route::get("/etudiants", Etudiant::class)->name("etudiants.index");
        // abort_unless(\Illuminate\Support\Facades\Gate::allows('superadmin'), 403);
        Route::get("/etudiants/{userId}/notes", NoteEtu::class)->name("etudiants.notes")->middleware('auth.admin');
        Route::get("/etudiants/{userId}/{parcourId}/{niveauId}/{anneeId}/releve", Releve::class)->name("etudiants.releve")->middleware('auth.admin');
        Route::get("/etudiants/{userId}/{parcourId}/{niveauId}/{anneeId}/certificat", CertificatScolarite::class)->name("etudiants.certificat")->middleware('auth.admin');
        
    });

    Route::group([
        "prefix" => "pedagogiques",
        'as' => 'pedagogiques.'
    ], function () {

        Route::get("/mention", Mentions::class)->name("mention.index");
        Route::get("/mention/{mentionId}/parcours", Parcours::class)->name("parcours.add");
        Route::get("/domaines", Domaines::class)->name("domaines.index");
        Route::get("/annees", AnneeUniversitaires::class)->name("annees.index");
        Route::get("/cours", AjoutNotes::class)->name("cours.index");
        Route::get("/ue/{ueId}/cours", Cours::class)->name("cours.add");
        Route::get("/ue", Ues::class)->name("ue.index");
        Route::get("/history", HistoriqueNotes::class)->name("history.index");
        Route::get("/semestre", Semestres::class)->name("semestre.index");
        Route::get("/resultat", Result::class)->name("resultat.index");
        Route::get("/status", NoteStatus::class)->name("status.index");
        Route::get("/rattrapage", Rattrapage::class)->name("rattrapage.index");
        Route::get("/prometu", PromtionEtu::class)->name("prometu.index");
        Route::get("/notes", ViewNote::class)->name("notes.index");

        Route::get("/cours/{coursId}/notes", Notes::class)->name("cours.notes");
    });
});

Route::group([
    "middleware" => ["auth", "auth.secretaire"],
    'as' => 'secretaire.'
], function () {

    Route::group([
        "prefix" => "gestion",
        'as' => 'gestion.'
    ], function () {

        Route::get("/cours", CoursSecretaire::class)->name("cours.index");
        
        Route::get("/cours/{coursId}/notes", Notes::class)->name("cours.notes");

        Route::get("/inscription", InfoEtu::class)->name("inscription.index");
        Route::get("/info", ListeNewEtu::class)->name("info.index");
    });
});

Route::group([
    "middleware" => ["auth", "auth.caf"],
    'as' => 'caf.'
], function () {

    Route::group([
        "prefix" => "caisse",
        'as' => 'caisse.'
    ], function () {
        Route::get("/infoetus", ListeNewEtu::class)->name("infoetus.index");
        Route::get("/histopay", HistoPay::class)->name("histopay.index");
        Route::get("/cours", CoursSecretaire::class)->name("cours.index");
        Route::get("/voiretats", VoirEtat::class)->name("voiretats.index");
        
        // Route::get("/liste", ListeNewEtu::class)->name("liste.index");

        Route::get("/cours/{coursId}/notes", Notes::class)->name("cours.notes");
        Route::get("/inscription", InscriptionField::class)->name("inscription.index");
        Route::get("/payment", PaymentEtu::class)->name("payment.index");
        Route::get("/payment/{userId}/{niveauId}/{anneeId}/etudiant", EtuPay::class)->name("payment.etudiant");
        Route::get("/todaypay", CafPay::class)->name("todaypay.index");
        Route::get("/generate-pdf", [PdfController::class, 'generatePdf'])->name("generate-pdf");
        Route::get("/diverspay", DiversPay::class)->name("diverspay.index");

    });
});

Route::group([
    "middleware" => ["auth", "auth.admin"],
    'as' => 'admin.'
], function () {

    Route::group([
        "prefix" => "habilitations",
        'as' => 'habilitations.'
    ], function () {

        Route::get("/utilisateurs", Utilisateurs::class)->name("users.index");
        Route::get("/figernote", FigerNote::class)->name("figer.index");
    });

    Route::group([
        "prefix" => "caisse",
        'as' => 'caisse.'
    ], function () {

        Route::get("/todaypayment", TodayPayment::class)->name("todaypayment.index");
        Route::get("/infoetu", ListeNewEtu::class)->name("infoetu.index");
        Route::get("/hitsoriquepayment", HistoPay::class)->name("historiquepayment.index");
        Route::get("/typepay", TypePay::class)->name("typepay.index");
        Route::get("/voiretat", VoirEtat::class)->name("voiretat.index");
        Route::get("/debt", DebtEtu::class)->name("debt.index");
        Route::get("/paystatus", PayStatus::class)->name("paystatus.index");
    });
});



Route::group([
    "middleware" => ["auth", "auth.enseignant"],
    'as' => 'enseignant.'
], function () {

    Route::group([
        "prefix" => "gestions",
        'as' => 'gestions.'
    ], function () {

        Route::get("/cours", CoursEnseignant::class)->name("cours.index");
        Route::get("/cours/{coursId}/notes", Notes::class)->name("cours.notes");
    });
});


