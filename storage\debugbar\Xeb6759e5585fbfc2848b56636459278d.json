{"__meta": {"id": "Xeb6759e5585fbfc2848b56636459278d", "datetime": "2025-07-24 12:30:13", "utime": 1753349413.487979, "method": "GET", "uri": "/caisse/infoetus", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753349409.080481, "end": 1753349413.48801, "duration": 4.407528877258301, "duration_str": "4.41s", "measures": [{"label": "Booting", "start": 1753349409.080481, "relative_start": 0, "end": 1753349410.343043, "relative_end": 1753349410.343043, "duration": 1.2625620365142822, "duration_str": "1.26s", "params": [], "collector": null}, {"label": "Application", "start": 1753349410.34396, "relative_start": 1.2634789943695068, "end": 1753349413.488013, "relative_end": 3.0994415283203125e-06, "duration": 3.1440529823303223, "duration_str": "3.14s", "params": [], "collector": null}]}, "memory": {"peak_usage": 27847632, "peak_usage_str": "27MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 7, "templates": [{"name": "C:\\xampp\\htdocs\\ImsaaProject\\vendor\\livewire\\livewire\\src/Macros/livewire-view-extends.blade.php (\\vendor\\livewire\\livewire\\src\\Macros\\livewire-view-extends.blade.php)", "param_count": 4, "params": ["view", "params", "slotOrSection", "manager"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\vendor\\livewire\\livewire\\src/Macros/livewire-view-extends.blade.php&line=0"}, {"name": "livewire.administration.contactEtu.index (\\resources\\views\\livewire\\administration\\contactEtu\\index.blade.php)", "param_count": 27, "params": ["etudiants", "parcourse", "niveaux", "annees", "livewireLayout", "errors", "_instance", "currentPage", "filtreParcours", "filtre<PERSON><PERSON>au", "filtreAnnee", "query", "perPage", "isLoading", "sortField", "sortDirection", "showFilters", "compactView", "totalEtudiants", "totalParParcours", "newEtus", "etus", "current_parcours", "current_niveau", "current_annee", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/administration/contactEtu/index.blade.php&line=0"}, {"name": "livewire.administration.contactEtu.liste (\\resources\\views\\livewire\\administration\\contactEtu\\liste.blade.php)", "param_count": 29, "params": ["__env", "app", "errors", "_instance", "etudiants", "parcourse", "niveaux", "annees", "livewireLayout", "currentPage", "filtreParcours", "filtre<PERSON><PERSON>au", "filtreAnnee", "query", "perPage", "isLoading", "sortField", "sortDirection", "showFilters", "compactView", "totalEtudiants", "totalParParcours", "newEtus", "etus", "current_parcours", "current_niveau", "current_annee", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/administration/contactEtu/liste.blade.php&line=0"}, {"name": "livewire::bootstrap (\\vendor\\livewire\\livewire\\src\\views\\pagination\\bootstrap.blade.php)", "param_count": 2, "params": ["paginator", "elements"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\vendor\\livewire\\livewire\\src\\views\\pagination/bootstrap.blade.php&line=0"}, {"name": "layouts.backend (\\resources\\views\\layouts\\backend.blade.php)", "param_count": 7, "params": ["__env", "app", "errors", "view", "params", "slotOrSection", "manager"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/layouts/backend.blade.php&line=0"}, {"name": "components.menu (\\resources\\views\\components\\menu.blade.php)", "param_count": 3, "params": ["attributes", "slot", "__laravel_slots"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/components/menu.blade.php&line=0"}, {"name": "components.rightHeader (\\resources\\views\\components\\rightHeader.blade.php)", "param_count": 3, "params": ["attributes", "slot", "__laravel_slots"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/components/rightHeader.blade.php&line=0"}]}, "route": {"uri": "GET caisse/infoetus", "middleware": "web, auth, auth.caf", "controller": "App\\Http\\Livewire\\ListeNewEtu@__invoke", "as": "caf.caisse.infoetus.index", "namespace": null, "prefix": "/caisse", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\vendor\\livewire\\livewire\\src\\Component.php&line=46\">\\vendor\\livewire\\livewire\\src\\Component.php:46-88</a>"}, "queries": {"nb_statements": 25, "nb_failed_statements": 0, "accumulated_duration": 1.33318, "accumulated_duration_str": "1.33s", "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.04985, "duration_str": "49.85ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:59", "connection": "imsaaapp", "start_percent": 0, "width_percent": 3.739}, {"sql": "select `roles`.*, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 1 and `nom` = 'superadmin' limit 1", "type": "query", "params": [], "bindings": ["1", "superadmin"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Models\\User.php", "line": 55}, {"index": 16, "namespace": null, "name": "\\app\\Providers\\AuthServiceProvider.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 553}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 425}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 395}], "duration": 0.16453, "duration_str": "165ms", "stmt_id": "\\app\\Models\\User.php:55", "connection": "imsaaapp", "start_percent": 3.739, "width_percent": 12.341}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`deleted_at` is null order by `nom` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 49}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.09948, "duration_str": "99.48ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:49", "connection": "imsaaapp", "start_percent": 16.08, "width_percent": 7.462}, {"sql": "select count(*) as aggregate from `users` where exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `role_id` = 5) and exists (select * from `inscription_students` where `users`.`id` = `inscription_students`.`user_id` and `annee_universitaire_id` = 6 and `inscription_students`.`deleted_at` is null) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "6"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 120}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 50}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.06012, "duration_str": "60.12ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:120", "connection": "imsaaapp", "start_percent": 23.542, "width_percent": 4.51}, {"sql": "select * from `users` where exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `role_id` = 5) and exists (select * from `inscription_students` where `users`.`id` = `inscription_students`.`user_id` and `annee_universitaire_id` = 6 and `inscription_students`.`deleted_at` is null) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "6"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 129}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 50}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.08595, "duration_str": "85.95ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:129", "connection": "imsaaapp", "start_percent": 28.052, "width_percent": 6.447}, {"sql": "select * from `inscription_students` where `inscription_students`.`user_id` in (4, 9, 12, 13, 14, 16, 17, 27, 28, 29, 39, 40, 41, 42, 43, 45, 46, 47, 48, 52, 53, 56, 57, 59, 66, 69, 71, 72, 73, 75, 77, 80, 85, 88, 91, 92, 93, 94, 95, 96, 98, 99, 100, 103, 104, 106, 107, 109, 110, 111, 112, 113, 114, 120, 130, 131, 132, 133, 139, 144, 185, 293, 295, 299, 300, 301, 303, 304, 305, 306, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 340, 342, 344, 345, 346, 347, 351, 352, 354, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 367, 368, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 383, 385, 387, 388, 389, 390, 392, 394, 396, 397, 398, 399, 400, 401, 402, 403, 404, 406, 407, 408, 409, 410, 411, 412, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426, 437, 438, 439, 441, 442, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 461, 462, 463, 465, 466, 467, 468, 469, 470, 471, 472, 473, 474, 475, 476, 477, 478, 479, 480, 481, 482, 483, 484, 485, 486, 487, 488, 489, 490, 491, 492, 493, 494, 495, 496, 497, 498, 499, 503, 504, 505, 506, 507, 508, 509) and `inscription_students`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 129}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 50}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0704, "duration_str": "70.4ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:129", "connection": "imsaaapp", "start_percent": 34.499, "width_percent": 5.281}, {"sql": "select * from `parcours` where `parcours`.`id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 15, 17, 19, 21, 22, 24) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 129}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 50}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.03447, "duration_str": "34.47ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:129", "connection": "imsaaapp", "start_percent": 39.779, "width_percent": 2.586}, {"sql": "select count(*) as aggregate from `users` where exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `role_id` = 5) and exists (select * from `inscription_students` where `users`.`id` = `inscription_students`.`user_id` and `annee_universitaire_id` = 6 and `inscription_students`.`deleted_at` is null) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "6"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 174}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.01659, "duration_str": "16.59ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:174", "connection": "imsaaapp", "start_percent": 42.365, "width_percent": 1.244}, {"sql": "select * from `users` where exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `role_id` = 5) and exists (select * from `inscription_students` where `users`.`id` = `inscription_students`.`user_id` and `annee_universitaire_id` = 6 and `inscription_students`.`deleted_at` is null) and `users`.`deleted_at` is null order by `nom` asc limit 15 offset 0", "type": "query", "params": [], "bindings": ["5", "6"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 174}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.07288, "duration_str": "72.88ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:174", "connection": "imsaaapp", "start_percent": 43.609, "width_percent": 5.467}, {"sql": "select * from `inscription_students` where `inscription_students`.`user_id` in (12, 16, 103, 114, 185, 327, 334, 396, 404, 410, 449, 465, 479, 492, 494) and `annee_universitaire_id` = 6 and `inscription_students`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 174}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0012900000000000001, "duration_str": "1.29ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:174", "connection": "imsaaapp", "start_percent": 49.076, "width_percent": 0.097}, {"sql": "select * from `parcours` where `parcours`.`id` in (1, 5, 8, 10, 12, 14, 15, 19, 22) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 174}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00117, "duration_str": "1.17ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:174", "connection": "imsaaapp", "start_percent": 49.173, "width_percent": 0.088}, {"sql": "select * from `mentions` where `mentions`.`id` in (2, 4, 5, 6, 7, 8, 9) and `mentions`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 30, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 174}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 32, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.04397, "duration_str": "43.97ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:174", "connection": "imsaaapp", "start_percent": 49.26, "width_percent": 3.298}, {"sql": "select * from `domaines` where `domaines`.`id` in (1, 2) and `domaines`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 35, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 174}, {"index": 36, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 37, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 38, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 39, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.04711, "duration_str": "47.11ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:174", "connection": "imsaaapp", "start_percent": 52.559, "width_percent": 3.534}, {"sql": "select `id`, `nom` from `niveaux` where `niveaux`.`id` in (1, 2, 3, 4) and `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 174}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.024300000000000002, "duration_str": "24.3ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:174", "connection": "imsaaapp", "start_percent": 56.092, "width_percent": 1.823}, {"sql": "select `id`, `nom` from `annee_universitaires` where `annee_universitaires`.`id` in (6) and `annee_universitaires`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 174}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00381, "duration_str": "3.81ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:174", "connection": "imsaaapp", "start_percent": 57.915, "width_percent": 0.286}, {"sql": "select * from `parcours` where `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 178}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00822, "duration_str": "8.22ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:178", "connection": "imsaaapp", "start_percent": 58.201, "width_percent": 0.617}, {"sql": "select * from `niveaux` where `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 179}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00538, "duration_str": "5.38ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:179", "connection": "imsaaapp", "start_percent": 58.817, "width_percent": 0.404}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`deleted_at` is null order by `nom` desc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\ListeNewEtu.php", "line": 180}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00583, "duration_str": "5.83ms", "stmt_id": "\\app\\Http\\Livewire\\ListeNewEtu.php:180", "connection": "imsaaapp", "start_percent": 59.221, "width_percent": 0.437}, {"sql": "select `roles`.*, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 1 and `nom` = 'superadmin' limit 1", "type": "query", "params": [], "bindings": ["1", "superadmin"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Models\\User.php", "line": 55}, {"index": 16, "namespace": null, "name": "\\app\\Providers\\AuthServiceProvider.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 553}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 425}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 395}], "duration": 0.11034999999999999, "duration_str": "110ms", "stmt_id": "\\app\\Models\\User.php:55", "connection": "imsaaapp", "start_percent": 59.658, "width_percent": 8.277}, {"sql": "select `roles`.*, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 1 and `nom` = 'superadmin' limit 1", "type": "query", "params": [], "bindings": ["1", "superadmin"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Models\\User.php", "line": 55}, {"index": 16, "namespace": null, "name": "\\app\\Providers\\AuthServiceProvider.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 553}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 425}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 395}], "duration": 0.08019, "duration_str": "80.19ms", "stmt_id": "\\app\\Models\\User.php:55", "connection": "imsaaapp", "start_percent": 67.935, "width_percent": 6.015}, {"sql": "select `roles`.*, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 1 and `nom` = 'superadmin' limit 1", "type": "query", "params": [], "bindings": ["1", "superadmin"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Models\\User.php", "line": 55}, {"index": 16, "namespace": null, "name": "\\app\\Providers\\AuthServiceProvider.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 553}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 425}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 395}], "duration": 0.09147, "duration_str": "91.47ms", "stmt_id": "\\app\\Models\\User.php:55", "connection": "imsaaapp", "start_percent": 73.95, "width_percent": 6.861}, {"sql": "select `roles`.*, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 1 and `nom` = 'superadmin' limit 1", "type": "query", "params": [], "bindings": ["1", "superadmin"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Models\\User.php", "line": 55}, {"index": 16, "namespace": null, "name": "\\app\\Providers\\AuthServiceProvider.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 553}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 425}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 395}], "duration": 0.05195, "duration_str": "51.95ms", "stmt_id": "\\app\\Models\\User.php:55", "connection": "imsaaapp", "start_percent": 80.811, "width_percent": 3.897}, {"sql": "select `roles`.*, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 1 and `nom` = 'superadmin' limit 1", "type": "query", "params": [], "bindings": ["1", "superadmin"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Models\\User.php", "line": 55}, {"index": 16, "namespace": null, "name": "\\app\\Providers\\AuthServiceProvider.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 553}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 425}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 395}], "duration": 0.04061, "duration_str": "40.61ms", "stmt_id": "\\app\\Models\\User.php:55", "connection": "imsaaapp", "start_percent": 84.708, "width_percent": 3.046}, {"sql": "select `roles`.*, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 1 and `nom` = 'superadmin' limit 1", "type": "query", "params": [], "bindings": ["1", "superadmin"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Models\\User.php", "line": 55}, {"index": 16, "namespace": null, "name": "\\app\\Providers\\AuthServiceProvider.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 553}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 425}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 395}], "duration": 0.056670000000000005, "duration_str": "56.67ms", "stmt_id": "\\app\\Models\\User.php:55", "connection": "imsaaapp", "start_percent": 87.754, "width_percent": 4.251}, {"sql": "select `roles`.*, `role_user`.`user_id` as `pivot_user_id`, `role_user`.`role_id` as `pivot_role_id` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Helpers\\helpers.php", "line": 18}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "duration": 0.10659, "duration_str": "107ms", "stmt_id": "\\app\\Helpers\\helpers.php:18", "connection": "imsaaapp", "start_percent": 92.005, "width_percent": 7.995}]}, "models": {"data": {"App\\Models\\Niveau": 9, "App\\Models\\Domaine": 2, "App\\Models\\Mention": 7, "App\\Models\\Parcour": 52, "App\\Models\\InscriptionStudent": 476, "App\\Models\\AnneeUniversitaire": 8, "App\\Models\\Role": 8, "App\\Models\\User": 248}, "count": 810}, "livewire": {"data": {"liste-new-etu #xW8R9QAk1jFSlGduwfdB": "array:5 [\n  \"data\" => array:20 [\n    \"currentPage\" => \"liste\"\n    \"filtreParcours\" => \"\"\n    \"filtreNiveau\" => \"\"\n    \"filtreAnnee\" => 6\n    \"query\" => \"\"\n    \"perPage\" => 15\n    \"isLoading\" => false\n    \"sortField\" => \"nom\"\n    \"sortDirection\" => \"asc\"\n    \"showFilters\" => true\n    \"compactView\" => false\n    \"totalEtudiants\" => 232\n    \"totalParParcours\" => array:14 [\n      \"MAT\" => 2\n      \"THR\" => 37\n      \"CM\" => 18\n      \"GFC\" => 17\n      \"TBA\" => 11\n      \"TD\" => 64\n      \"GBC\" => 11\n      \"GPM\" => 5\n      \"GEE\" => 18\n      \"GRT\" => 13\n      \"LCI\" => 24\n      \"ISE\" => 5\n      \"ADA\" => 6\n      \"ICA\" => 1\n    ]\n    \"newEtus\" => []\n    \"etus\" => []\n    \"current_parcours\" => null\n    \"current_niveau\" => null\n    \"current_annee\" => null\n    \"page\" => 1\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"liste-new-etu\"\n  \"view\" => \"livewire.administration.contactEtu.index\"\n  \"component\" => \"App\\Http\\Livewire\\ListeNewEtu\"\n  \"id\" => \"xW8R9QAk1jFSlGduwfdB\"\n]"}, "count": 1}, "gate": {"count": 7, "messages": [{"message": "[ability => caf, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1360593025 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"3 characters\">caf</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1360593025\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753349411.070634}, {"message": "[ability => admin, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-307886647 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">admin</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-307886647\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753349413.000804}, {"message": "[ability => enseignant, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2121575831 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">enseignant</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2121575831\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753349413.087298}, {"message": "[ability => deraq, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2003204126 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"5 characters\">deraq</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2003204126\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753349413.184022}, {"message": "[ability => caf, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1266520441 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"3 characters\">caf</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1266520441\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753349413.244425}, {"message": "[ability => secretaire, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1731588216 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">secretaire</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1731588216\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753349413.291309}, {"message": "[ability => etudiant, result => true, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-793384847 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">etudiant</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-793384847\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1753349413.355895}]}, "session": {"_token": "VtnJlwxOpd0hRXXY8oKOSLlbl0TFeAO2LM004ptd", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/caisse/infoetus\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1753344491\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/caisse/infoetus", "status_code": "<pre class=sf-dump id=sf-dump-1727620528 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1727620528\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-177117442 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-177117442\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-592456624 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-592456624\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImMvL1R3M0FhNTB2MVhmV3VHWEI4ZFE9PSIsInZhbHVlIjoiN1Q2SjlBSkVMbTFNMGNNQWtZWnprRFBIdjdacEVVNCtuWnJRMjIxbEN4ejBnUjNqdVNBRlpFNVFiQWt2VWxkY3gydkZESnNwTEwvMTFwQ0JqQWdzemxmUVNpYm5hNFI3OE1hWkdsWTlvaGVrSFNiT1QwVUs1NzVmYTdFdzRERXkiLCJtYWMiOiIwNDVhYzZhNTU1YjZkODNiYzkxM2QxOTRlY2I0ZjMzOWJjNTU5MzY0M2ZjMGMzMTA4YjRmOGI5YTYwNmZmYzk4IiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6ImQ3YjZtaDhhdFRSbG9jNTYxM0NBSEE9PSIsInZhbHVlIjoiQ1ViQWkwb2tSZWJ4RjZEQkFjMVg3VVgxOUE2dmJOcnFKQlJCYmJhcGpqSzdHSXJEVzRocDhIcXVGYWNHMFdwb2kxemprc3dRcVRrRlljdWpnUlVuN3hoaTQveGY3Mm1BT2NVYjZ2blRLUkUzaUs3cjZpcm4rd1RqTUlMRVNrRHciLCJtYWMiOiI2ZmFkNTQ0NTljMWQ3ZmY0M2E5ZThhZDEzZTRlYWQ0ZTBjNWJmMjhkM2RiMTk0NjRkNDRlOTg1MDA4ODVkNjE3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1305554540 data-indent-pad=\"  \"><span class=sf-dump-note>array:32</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"35 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">58151</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"16 characters\">/caisse/infoetus</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"45 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"16 characters\">/caisse/infoetus</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"26 characters\">/index.php/caisse/infoetus</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImMvL1R3M0FhNTB2MVhmV3VHWEI4ZFE9PSIsInZhbHVlIjoiN1Q2SjlBSkVMbTFNMGNNQWtZWnprRFBIdjdacEVVNCtuWnJRMjIxbEN4ejBnUjNqdVNBRlpFNVFiQWt2VWxkY3gydkZESnNwTEwvMTFwQ0JqQWdzemxmUVNpYm5hNFI3OE1hWkdsWTlvaGVrSFNiT1QwVUs1NzVmYTdFdzRERXkiLCJtYWMiOiIwNDVhYzZhNTU1YjZkODNiYzkxM2QxOTRlY2I0ZjMzOWJjNTU5MzY0M2ZjMGMzMTA4YjRmOGI5YTYwNmZmYzk4IiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6ImQ3YjZtaDhhdFRSbG9jNTYxM0NBSEE9PSIsInZhbHVlIjoiQ1ViQWkwb2tSZWJ4RjZEQkFjMVg3VVgxOUE2dmJOcnFKQlJCYmJhcGpqSzdHSXJEVzRocDhIcXVGYWNHMFdwb2kxemprc3dRcVRrRlljdWpnUlVuN3hoaTQveGY3Mm1BT2NVYjZ2blRLUkUzaUs3cjZpcm4rd1RqTUlMRVNrRHciLCJtYWMiOiI2ZmFkNTQ0NTljMWQ3ZmY0M2E5ZThhZDEzZTRlYWQ0ZTBjNWJmMjhkM2RiMTk0NjRkNDRlOTg1MDA4ODVkNjE3IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753349409.0805</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753349409</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1305554540\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VtnJlwxOpd0hRXXY8oKOSLlbl0TFeAO2LM004ptd</span>\"\n  \"<span class=sf-dump-key>scolarite_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RKkczZOcD1TSWkk2eKq2gL5rBSt8JPLoI0Me9VkZ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1767810364 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 24 Jul 2025 09:30:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InpyT0VvNGNpd3E0dCtyUng0Nlk3cXc9PSIsInZhbHVlIjoiYmR4dmxXd015VHBVb1JDUzBrMmYwbVlubEZLTGhWVVVlQ1hGb3JLWVpVbEYzMFMvSnZLRUFkRVBLSXF3RjQ2Y08vcFNwOUQzZWNRUFpUQkwzMmx3UW8rVmpwQ3pETzNBNFFPMWFiaTdheXlLSXJtNnBrV3BkdnRXOWRuZzk1SlciLCJtYWMiOiJlZGQyNGJhNTMxMDg3MTljOTE2YzdlYTUwMDYwOTdhZDkxOTg2YzBhZTk4MDFlNmM3NTExODBkODQ2OGYyZjAzIiwidGFnIjoiIn0%3D; expires=Thu, 24 Jul 2025 11:30:13 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"451 characters\">scolarite_imsaa_session=eyJpdiI6IngxVWFwZkNveFBuMXFrV0pwRkJadkE9PSIsInZhbHVlIjoid2l3TEZOU3JabExKUnZJaVdQbVpPazA0Z3hZQXdIeUNHQzAxTjBDMkVDeXBVZWt1NlkwbHNsQlhReFQzMElPM3NXVEI5cnIxWWoycU5UcGZTWW50RGw1UDE3ZHlYYi9QWEpZZUY4cnVvWGpVOGJzdjEwd3UzbW5Dd2taWlZVZ0UiLCJtYWMiOiIxMjIyZjA5NDhmNWY3NWYzZmI3NGVhZTUwNDM0M2M3M2NiZDE0ODdjNGQ1ZTZmZTg1ZDU0ZjAyYjhkODc3ZGExIiwidGFnIjoiIn0%3D; expires=Thu, 24 Jul 2025 11:30:13 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InpyT0VvNGNpd3E0dCtyUng0Nlk3cXc9PSIsInZhbHVlIjoiYmR4dmxXd015VHBVb1JDUzBrMmYwbVlubEZLTGhWVVVlQ1hGb3JLWVpVbEYzMFMvSnZLRUFkRVBLSXF3RjQ2Y08vcFNwOUQzZWNRUFpUQkwzMmx3UW8rVmpwQ3pETzNBNFFPMWFiaTdheXlLSXJtNnBrV3BkdnRXOWRuZzk1SlciLCJtYWMiOiJlZGQyNGJhNTMxMDg3MTljOTE2YzdlYTUwMDYwOTdhZDkxOTg2YzBhZTk4MDFlNmM3NTExODBkODQ2OGYyZjAzIiwidGFnIjoiIn0%3D; expires=Thu, 24-Jul-2025 11:30:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"423 characters\">scolarite_imsaa_session=eyJpdiI6IngxVWFwZkNveFBuMXFrV0pwRkJadkE9PSIsInZhbHVlIjoid2l3TEZOU3JabExKUnZJaVdQbVpPazA0Z3hZQXdIeUNHQzAxTjBDMkVDeXBVZWt1NlkwbHNsQlhReFQzMElPM3NXVEI5cnIxWWoycU5UcGZTWW50RGw1UDE3ZHlYYi9QWEpZZUY4cnVvWGpVOGJzdjEwd3UzbW5Dd2taWlZVZ0UiLCJtYWMiOiIxMjIyZjA5NDhmNWY3NWYzZmI3NGVhZTUwNDM0M2M3M2NiZDE0ODdjNGQ1ZTZmZTg1ZDU0ZjAyYjhkODc3ZGExIiwidGFnIjoiIn0%3D; expires=Thu, 24-Jul-2025 11:30:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1767810364\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-900304904 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VtnJlwxOpd0hRXXY8oKOSLlbl0TFeAO2LM004ptd</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/caisse/infoetus</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1753344491</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-900304904\", {\"maxDepth\":0})</script>\n"}}